import { RemoteConnectionProps, RemoteConnectionState } from '../RemoteConnection';
/**
 * Initializes the connector for MetaMask remote communication based on provided options.
 *
 * @param state Current state of the RemoteConnection class instance.
 * @param options Configuration options for the remote connection.
 * @returns void
 */
export declare function initializeConnector(state: RemoteConnectionState, options: RemoteConnectionProps): void;
//# sourceMappingURL=initializeConnector.d.ts.map