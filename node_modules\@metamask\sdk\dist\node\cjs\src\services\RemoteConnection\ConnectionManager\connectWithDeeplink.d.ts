import { RemoteConnectionState } from '../RemoteConnection';
/**
 * Generates and opens a universal link or deeplink for MetaMask connection based on given parameters.
 *
 * @param state Current state of the RemoteConnection class instance.
 * @param linkParams A string representing the parameters used to form the universal and deep links.
 * @returns Promise<void>
 */
export declare function connectWithDeeplink(state: RemoteConnectionState, linkParams: string): Promise<void>;
//# sourceMappingURL=connectWithDeeplink.d.ts.map