{"version": 3, "file": "Communicator.d.ts", "sourceRoot": "", "sources": ["../../../src/core/communicator/Communicator.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAG3D,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AAGtE,MAAM,MAAM,mBAAmB,GAAG;IAChC,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,WAAW,CAAC;IACtB,UAAU,EAAE,UAAU,CAAC;CACxB,CAAC;AAEF;;;;;;;;GAQG;AACH,qBAAa,YAAY;IACvB,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAc;IACvC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAa;IACxC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAM;IAC1B,OAAO,CAAC,KAAK,CAAuB;IACpC,OAAO,CAAC,SAAS,CAAwE;gBAE7E,EAAE,GAAiB,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE,mBAAmB;IAM5E;;OAEG;IACH,WAAW,YAAmB,OAAO,mBAGnC;IAEF;;OAEG;IACH,6BAA6B,GAAU,CAAC,SAAS,OAAO,WAC7C,OAAO,GAAG;QAAE,EAAE,EAAE,SAAS,CAAA;KAAE,KACnC,OAAO,CAAC,CAAC,CAAC,CAIX;IAEF;;OAEG;IACH,SAAS,GAAU,CAAC,SAAS,OAAO,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,KAAG,OAAO,CAAC,CAAC,CAAC,CAgBtF;IAEF;;OAEG;IACH,OAAO,CAAC,UAAU,CAUhB;IAEF;;OAEG;IACH,kBAAkB,QAAa,OAAO,CAAC,MAAM,CAAC,CA6B5C;CACH"}