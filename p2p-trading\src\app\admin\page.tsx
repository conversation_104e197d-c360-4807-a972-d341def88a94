'use client'

import { useAccount } from 'wagmi'
import { useEffect, useState } from 'react'
import WalletConnect from '@/components/WalletConnect'
import AdminVerificationDashboard from '@/components/AdminVerificationDashboard'

export default function AdminPage() {
  const { address, isConnected } = useAccount()
  const [mounted, setMounted] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (isConnected && address) {
      const adminWallet = process.env.NEXT_PUBLIC_ADMIN_WALLET_ADDRESS?.toLowerCase()
      const userWallet = address.toLowerCase()
      setIsAdmin(adminWallet === userWallet)
    } else {
      setIsAdmin(false)
    }
  }, [isConnected, address])

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!isConnected) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-8">
        <div className="text-center max-w-md mx-auto">
          <h1 className="text-4xl font-bold mb-4">
            <span className="text-blue-400">Admin</span> Dashboard
          </h1>
          <p className="text-gray-400 mb-8">
            Connect your admin wallet to access the dashboard
          </p>
          <WalletConnect />
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-8">
        <div className="text-center max-w-md mx-auto">
          <div className="mb-6 p-6 bg-red-900/20 border border-red-500/30 rounded-lg">
            <h1 className="text-3xl font-bold mb-4 text-red-400">❌ Access Denied</h1>
            <p className="text-gray-300 mb-4">
              You are not authorized to access the admin dashboard.
            </p>
            <div className="text-sm text-gray-500">
              <p>Connected wallet: {address?.slice(0, 8)}...{address?.slice(-6)}</p>
              <p>Admin wallet required</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <a 
              href="/"
              className="btn-primary inline-block px-6 py-3"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Admin Header */}
      <div className="bg-blue-900/20 border-b border-blue-500/30 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-blue-400">Admin Dashboard</h1>
            <p className="text-sm text-gray-400">
              Wallet: {address?.slice(0, 8)}...{address?.slice(-6)}
            </p>
          </div>
          <div className="flex gap-4">
            <a 
              href="/"
              className="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800"
            >
              ← Home
            </a>
          </div>
        </div>
      </div>

      {/* Admin Content */}
      <div className="p-6">
        <AdminVerificationDashboard />
      </div>
    </div>
  )
}
