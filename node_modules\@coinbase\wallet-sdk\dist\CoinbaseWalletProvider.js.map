{"version": 3, "file": "CoinbaseWalletProvider.js", "sourceRoot": "", "sources": ["../src/CoinbaseWalletProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAChG,OAAO,EAAE,YAAY,EAAE,MAAM,oCAAoC,CAAC;AAClE,OAAO,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAE1D,OAAO,EAIL,oBAAoB,GAGrB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AACzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,+BAA+B,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAErF,MAAM,OAAO,sBAAuB,SAAQ,oBAAoB;IAO9D,YAAY,EAAkF;YAAlF,EAAE,QAAQ,OAAwE,EAAtE,kBAAsC,EAAtC,EAAc,OAAO,OAAiB,EAAZ,UAAU,cAAxB,WAA0B,CAAF;QAC1D,KAAK,EAAE,CAAC;QAHF,WAAM,GAAkB,IAAI,CAAC;QA4E5B,qBAAgB,GAAG,IAAI,CAAC;QAxE/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC;YACnC,GAAG,EAAE,OAAO;YACZ,QAAQ;YACR,UAAU;SACX,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,cAAc,EAAE,CAAC;QACpC,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,OAAO,CAAI,IAAsB;QAC5C,IAAI,CAAC;YACH,+BAA+B,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;oBACpB,KAAK,qBAAqB,CAAC,CAAC,CAAC;wBAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;wBAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;wBAC3C,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;wBACrB,eAAe,CAAC,UAAU,CAAC,CAAC;wBAC5B,MAAM;oBACR,CAAC;oBACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;wBACxB,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;wBAC/C,MAAM,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,wBAAwB;wBAClF,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAwC;wBAC5F,MAAM,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,+CAA+C;wBAChF,OAAO,MAAW,CAAC;oBACrB,CAAC;oBACD,KAAK,uBAAuB;wBAC1B,OAAO,eAAe,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;oBAClD,KAAK,aAAa;wBAChB,OAAO,CAAM,CAAC,CAAC,gBAAgB;oBACjC,KAAK,aAAa;wBAChB,OAAO,mBAAmB,CAAC,CAAC,CAAM,CAAC,CAAC,gBAAgB;oBACtD,OAAO,CAAC,CAAC,CAAC;wBACR,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,CACxC,sDAAsD,CACvD,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,EAAE,IAAI,EAAE,GAAG,KAA0B,CAAC;YAC5C,IAAI,IAAI,KAAK,kBAAkB,CAAC,QAAQ,CAAC,YAAY;gBAAE,IAAI,CAAC,UAAU,EAAE,CAAC;YACzE,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,6EAA6E;IACtE,KAAK,CAAC,MAAM;QACjB,OAAO,CAAC,IAAI,CACV,gGAAgG,CACjG,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YACxB,MAAM,EAAE,qBAAqB;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;;QACd,MAAM,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,OAAO,EAAE,CAAA,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,8BAA8B,CAAC,CAAC,CAAC;IAChG,CAAC;IAIO,sBAAsB,CAAC,gBAAkC;QAC/D,OAAO,eAAe,CAAC;YACrB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB;YAChB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SAC/B,CAAC,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,UAAsB;QACvC,OAAO,YAAY,CAAC;YAClB,UAAU;YACV,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SAC/B,CAAC,CAAC;IACL,CAAC;CACF"}