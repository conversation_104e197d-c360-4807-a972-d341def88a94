-- Fix Supabase Storage Issues - Run this in Supabase SQL Editor

-- Create storage bucket for payment proofs (if not exists)
INSERT INTO storage.buckets (id, name, public) 
VALUES ('payment_proofs', 'payment_proofs', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Drop existing storage policies
DROP POLICY IF EXISTS "Users can upload payment proofs" ON storage.objects;
DROP POLICY IF EXISTS "Users can view payment proofs" ON storage.objects;

-- Create new storage policies (more permissive for testing)
CREATE POLICY "Anyone can upload payment proofs" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'payment_proofs');

CREATE POLICY "Anyone can view payment proofs" ON storage.objects
    FOR SELECT USING (bucket_id = 'payment_proofs');

CREATE POLICY "Anyone can update payment proofs" ON storage.objects
    FOR UPDATE USING (bucket_id = 'payment_proofs');

CREATE POLICY "Anyone can delete payment proofs" ON storage.objects
    FOR DELETE USING (bucket_id = 'payment_proofs');

-- Add confirmed_by_seller column to proofs table
ALTER TABLE proofs ADD COLUMN IF NOT EXISTS confirmed_by_seller BOOLEAN DEFAULT false;
