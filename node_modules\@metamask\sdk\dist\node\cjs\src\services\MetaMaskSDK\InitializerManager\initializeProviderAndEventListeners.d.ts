import { MetaMaskSDK } from '../../../sdk';
/**
 * Initializes the MetaMask provider and event listeners for the SDK instance.
 *
 * This function first initializes the active provider by calling 'initializeProvider' with relevant options.
 * It then sets up event listeners for the SDK instance by calling 'initEventListeners'.
 *
 * @param instance The MetaMaskSDK instance for which the provider and event listeners will be initialized.
 * @returns void
 * @async
 */
export declare function initializeProviderAndEventListeners(instance: MetaMaskSDK): Promise<void>;
//# sourceMappingURL=initializeProviderAndEventListeners.d.ts.map