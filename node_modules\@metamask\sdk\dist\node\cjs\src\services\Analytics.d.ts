import { AnalyticsProps, TrackingEvents } from '@metamask/sdk-communication-layer';
export declare const ANALYTICS_CONSTANTS: {
    DEFAULT_ID: string;
    NO_VERSION: string;
};
export declare class Analytics {
    private serverURL;
    private enabled;
    private readonly originatorInfo;
    constructor({ serverUrl, enabled, originatorInfo, }: {
        serverUrl: string;
        originatorInfo: AnalyticsProps['originatorInfo'];
        enabled?: boolean;
    });
    send({ event, params, }: {
        event: TrackingEvents;
        params?: Record<string, unknown>;
    }): void;
}
//# sourceMappingURL=Analytics.d.ts.map