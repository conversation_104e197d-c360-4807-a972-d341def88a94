"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("cross-fetch"),t=require("debug"),n=require("buffer"),r=require("node:crypto"),i=require("eventemitter2"),o=require("uuid"),s=require("socket.io-client"),a=require("events"),c=require("stream"),d=require("util");function l(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;var u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var f={},p={},m={};function g(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function v(e){if("boolean"!=typeof e)throw new Error(`boolean expected, not ${e}`)}function y(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function E(e,...t){if(!y(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function b(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("hash must be wrapped by utils.wrapConstructor");g(e.outputLen),g(e.blockLen)}function w(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function S(e,t){E(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}Object.defineProperty(m,"__esModule",{value:!0}),m.isBytes=y,m.number=g,m.bool=v,m.bytes=E,m.hash=b,m.exists=w,m.output=S;const _={number:g,bool:v,bytes:E,hash:b,exists:w,output:S};m.default=_,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.wrapCipher=e.Hash=e.nextTick=e.isLE=e.createView=e.u32=e.u16=e.u8=void 0,e.bytesToHex=r,e.hexToBytes=s,e.hexToNumber=a,e.bytesToNumberBE=function(e){return a(r(e))},e.numberToBytesBE=function(e,t){return s(e.toString(16).padStart(2*t,"0"))},e.asyncLoop=async function(t,n,r){let i=Date.now();for(let o=0;o<t;o++){r(o);const t=Date.now()-i;t>=0&&t<n||(await(0,e.nextTick)(),i+=t)}},e.utf8ToBytes=c,e.bytesToUtf8=function(e){return(new TextDecoder).decode(e)},e.toBytes=function(e){if("string"==typeof e)e=c(e);else{if(!(0,t.isBytes)(e))throw new Error("Uint8Array expected, got "+typeof e);e=l(e)}return e},e.concatBytes=function(...e){let n=0;for(let r=0;r<e.length;r++){const i=e[r];(0,t.bytes)(i),n+=i.length}const r=new Uint8Array(n);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r},e.checkOpts=function(e,t){if(null==t||"object"!=typeof t)throw new Error("options must be defined");return Object.assign(e,t)},e.equalBytes=function(e,t){if(e.length!==t.length)return!1;let n=0;for(let r=0;r<e.length;r++)n|=e[r]^t[r];return 0===n},e.setBigUint64=d,e.u64Lengths=function(t,n){const r=new Uint8Array(16),i=(0,e.createView)(r);return d(i,0,BigInt(n?n.length:0),!0),d(i,8,BigInt(t.length),!0),r},e.isAligned32=function(e){return e.byteOffset%4==0},e.copyBytes=l,e.clean=function(...e){for(let t=0;t<e.length;t++)e[t].fill(0)};const t=m;e.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength);e.u16=e=>new Uint16Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/2));e.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4));if(e.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0],!e.isLE)throw new Error("Non little-endian hardware is not supported");const n=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function r(e){(0,t.bytes)(e);let r="";for(let t=0;t<e.length;t++)r+=n[e[t]];return r}const i={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function o(e){return e>=i._0&&e<=i._9?e-i._0:e>=i._A&&e<=i._F?e-(i._A-10):e>=i._a&&e<=i._f?e-(i._a-10):void 0}function s(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=o(e.charCodeAt(i)),s=o(e.charCodeAt(i+1));if(void 0===n||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+s}return r}function a(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);return BigInt(""===e?"0":`0x${e}`)}function c(e){if("string"!=typeof e)throw new Error("string expected, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}e.nextTick=async()=>{};e.Hash=class{};function d(e,t,n,r){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,r);const i=BigInt(32),o=BigInt(4294967295),s=Number(n>>i&o),a=Number(n&o),c=r?4:0,d=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+d,a,r)}function l(e){return Uint8Array.from(e)}e.wrapCipher=(e,t)=>(Object.assign(t,e),t)}(p);var C={},x={};Object.defineProperty(x,"__esModule",{value:!0}),x.AEAD_TAG_LENGTH=x.XCHACHA20_NONCE_LENGTH=x.CURVE25519_PUBLIC_KEY_SIZE=x.ETH_PUBLIC_KEY_SIZE=x.UNCOMPRESSED_PUBLIC_KEY_SIZE=x.COMPRESSED_PUBLIC_KEY_SIZE=x.SECRET_KEY_LENGTH=void 0,x.SECRET_KEY_LENGTH=32,x.COMPRESSED_PUBLIC_KEY_SIZE=33,x.UNCOMPRESSED_PUBLIC_KEY_SIZE=65,x.ETH_PUBLIC_KEY_SIZE=64,x.CURVE25519_PUBLIC_KEY_SIZE=32,x.XCHACHA20_NONCE_LENGTH=24,x.AEAD_TAG_LENGTH=16,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ephemeralKeySize=e.symmetricNonceLength=e.symmetricAlgorithm=e.isHkdfKeyCompressed=e.isEphemeralKeyCompressed=e.ellipticCurve=e.ECIES_CONFIG=void 0;var t=x,n=function(){this.ellipticCurve="secp256k1",this.isEphemeralKeyCompressed=!1,this.isHkdfKeyCompressed=!1,this.symmetricAlgorithm="aes-256-gcm",this.symmetricNonceLength=16};e.ECIES_CONFIG=new n;e.ellipticCurve=function(){return e.ECIES_CONFIG.ellipticCurve};e.isEphemeralKeyCompressed=function(){return e.ECIES_CONFIG.isEphemeralKeyCompressed};e.isHkdfKeyCompressed=function(){return e.ECIES_CONFIG.isHkdfKeyCompressed};e.symmetricAlgorithm=function(){return e.ECIES_CONFIG.symmetricAlgorithm};e.symmetricNonceLength=function(){return e.ECIES_CONFIG.symmetricNonceLength};e.ephemeralKeySize=function(){var n={secp256k1:e.ECIES_CONFIG.isEphemeralKeyCompressed?t.COMPRESSED_PUBLIC_KEY_SIZE:t.UNCOMPRESSED_PUBLIC_KEY_SIZE,x25519:t.CURVE25519_PUBLIC_KEY_SIZE,ed25519:t.CURVE25519_PUBLIC_KEY_SIZE};if(e.ECIES_CONFIG.ellipticCurve in n)return n[e.ECIES_CONFIG.ellipticCurve];throw new Error("Not implemented")}}(C);var k={},M={},A={},T={},I={},P={};Object.defineProperty(P,"__esModule",{value:!0}),P.crypto=void 0;const R=r;P.crypto=R&&"object"==typeof R&&"webcrypto"in R?R.webcrypto:R&&"object"==typeof R&&"randomBytes"in R?R:void 0,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.gcm=e.ctr=e.cbc=e.utils=void 0,e.randomBytes=i,e.getWebcryptoSubtle=o,e.managedNonce=function(e){return(0,n.number)(e.nonceLength),(t,...n)=>({encrypt(o,...s){const{nonceLength:a}=e,c=i(a),d=e(t,c,...n).encrypt(o,...s),l=(0,r.concatBytes)(c,d);return d.fill(0),l},decrypt(r,...i){const{nonceLength:o}=e,s=r.subarray(0,o),a=r.subarray(o);return e(t,s,...n).decrypt(a,...i)}})};const t=P,n=m,r=p;function i(e=32){if(t.crypto&&"function"==typeof t.crypto.getRandomValues)return t.crypto.getRandomValues(new Uint8Array(e));if(t.crypto&&"function"==typeof t.crypto.randomBytes)return t.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}function o(){if(t.crypto&&"object"==typeof t.crypto.subtle&&null!=t.crypto.subtle)return t.crypto.subtle;throw new Error("crypto.subtle must be defined")}e.utils={async encrypt(e,t,n,r){const i=o(),s=await i.importKey("raw",e,t,!0,["encrypt"]),a=await i.encrypt(n,s,r);return new Uint8Array(a)},async decrypt(e,t,n,r){const i=o(),s=await i.importKey("raw",e,t,!0,["decrypt"]),a=await i.decrypt(n,s,r);return new Uint8Array(a)}};const s={CBC:"AES-CBC",CTR:"AES-CTR",GCM:"AES-GCM"};function a(t){return(r,i,o)=>{(0,n.bytes)(r),(0,n.bytes)(i);const a={name:t,length:8*r.length},c=function(e,t,n){if(e===s.CBC)return{name:s.CBC,iv:t};if(e===s.CTR)return{name:s.CTR,counter:t,length:64};if(e===s.GCM)return n?{name:s.GCM,iv:t,additionalData:n}:{name:s.GCM,iv:t};throw new Error("unknown aes block mode")}(t,i,o);return{encrypt:t=>((0,n.bytes)(t),e.utils.encrypt(r,a,c,t)),decrypt:t=>((0,n.bytes)(t),e.utils.decrypt(r,a,c,t))}}}e.cbc=a(s.CBC),e.ctr=a(s.CTR),e.gcm=a(s.GCM)}(I);var O={},N={},D={},L={};function B(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function K(e){if("boolean"!=typeof e)throw new Error(`boolean expected, not ${e}`)}function j(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function $(e,...t){if(!j(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function H(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");B(e.outputLen),B(e.blockLen)}function U(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function F(e,t){$(e);const n=t.outputLen;if(e.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}Object.defineProperty(L,"__esModule",{value:!0}),L.isBytes=j,L.number=B,L.bool=K,L.bytes=$,L.hash=H,L.exists=U,L.output=F;const q={number:B,bool:K,bytes:$,hash:H,exists:U,output:F};L.default=q;var z={},W={};Object.defineProperty(W,"__esModule",{value:!0}),W.crypto=void 0;const V=r;W.crypto=V&&"object"==typeof V&&"webcrypto"in V?V.webcrypto:V&&"object"==typeof V&&"randomBytes"in V?V:void 0,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Hash=e.nextTick=e.byteSwapIfBE=e.byteSwap=e.isLE=e.rotl=e.rotr=e.createView=e.u32=e.u8=void 0,e.isBytes=function(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name},e.byteSwap32=function(t){for(let n=0;n<t.length;n++)t[n]=(0,e.byteSwap)(t[n])},e.bytesToHex=function(e){(0,n.bytes)(e);let t="";for(let n=0;n<e.length;n++)t+=r[e[n]];return t},e.hexToBytes=function(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=o(e.charCodeAt(i)),s=o(e.charCodeAt(i+1));if(void 0===n||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+s}return r},e.asyncLoop=async function(t,n,r){let i=Date.now();for(let o=0;o<t;o++){r(o);const t=Date.now()-i;t>=0&&t<n||(await(0,e.nextTick)(),i+=t)}},e.utf8ToBytes=s,e.toBytes=a,e.concatBytes=function(...e){let t=0;for(let r=0;r<e.length;r++){const i=e[r];(0,n.bytes)(i),t+=i.length}const r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r},e.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==c.call(t))throw new Error("Options should be object or undefined");return Object.assign(e,t)},e.wrapConstructor=function(e){const t=t=>e().update(a(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t},e.wrapConstructorWithOpts=function(e){const t=(t,n)=>e(n).update(a(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},e.wrapXOFConstructorWithOpts=function(e){const t=(t,n)=>e(n).update(a(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},e.randomBytes=function(e=32){if(t.crypto&&"function"==typeof t.crypto.getRandomValues)return t.crypto.getRandomValues(new Uint8Array(e));if(t.crypto&&"function"==typeof t.crypto.randomBytes)return t.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")};const t=W,n=L;e.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength);e.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4));e.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength);e.rotr=(e,t)=>e<<32-t|e>>>t;e.rotl=(e,t)=>e<<t|e>>>32-t>>>0,e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0];e.byteSwap=e=>e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255,e.byteSwapIfBE=e.isLE?e=>e:t=>(0,e.byteSwap)(t);const r=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));const i={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function o(e){return e>=i._0&&e<=i._9?e-i._0:e>=i._A&&e<=i._F?e-(i._A-10):e>=i._a&&e<=i._f?e-(i._a-10):void 0}function s(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}function a(e){return"string"==typeof e&&(e=s(e)),(0,n.bytes)(e),e}e.nextTick=async()=>{};e.Hash=class{clone(){return this._cloneInto()}};const c={}.toString}(z),Object.defineProperty(D,"__esModule",{value:!0}),D.HashMD=D.Maj=D.Chi=void 0;const G=L,Y=z;D.Chi=(e,t,n)=>e&t^~e&n;D.Maj=(e,t,n)=>e&t^e&n^t&n;D.HashMD=class extends Y.Hash{constructor(e,t,n,r){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=(0,Y.createView)(this.buffer)}update(e){(0,G.exists)(this);const{view:t,buffer:n,blockLen:r}=this,i=(e=(0,Y.toBytes)(e)).length;for(let o=0;o<i;){const s=Math.min(r-this.pos,i-o);if(s!==r)n.set(e.subarray(o,o+s),this.pos),this.pos+=s,o+=s,this.pos===r&&(this.process(t,0),this.pos=0);else{const t=(0,Y.createView)(e);for(;r<=i-o;o+=r)this.process(t,o)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){(0,G.exists)(this),(0,G.output)(e,this),this.finished=!0;const{buffer:t,view:n,blockLen:r,isLE:i}=this;let{pos:o}=this;t[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>r-o&&(this.process(n,0),o=0);for(let e=o;e<r;e++)t[e]=0;!function(e,t,n,r){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,r);const i=BigInt(32),o=BigInt(4294967295),s=Number(n>>i&o),a=Number(n&o),c=r?4:0,d=r?0:4;e.setUint32(t+c,s,r),e.setUint32(t+d,a,r)}(n,r-8,BigInt(8*this.length),i),this.process(n,0);const s=(0,Y.createView)(e),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const c=a/4,d=this.get();if(c>d.length)throw new Error("_sha2: outputLen bigger than state");for(let e=0;e<c;e++)s.setUint32(4*e,d[e],i)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:n,length:r,finished:i,destroyed:o,pos:s}=this;return e.length=r,e.pos=s,e.finished=i,e.destroyed=o,r%t&&e.buffer.set(n),e}};var Z={};Object.defineProperty(Z,"__esModule",{value:!0}),Z.add5L=Z.add5H=Z.add4H=Z.add4L=Z.add3H=Z.add3L=Z.rotlBL=Z.rotlBH=Z.rotlSL=Z.rotlSH=Z.rotr32L=Z.rotr32H=Z.rotrBL=Z.rotrBH=Z.rotrSL=Z.rotrSH=Z.shrSL=Z.shrSH=Z.toBig=void 0,Z.fromBig=X,Z.split=ee,Z.add=pe;const J=BigInt(2**32-1),Q=BigInt(32);function X(e,t=!1){return t?{h:Number(e&J),l:Number(e>>Q&J)}:{h:0|Number(e>>Q&J),l:0|Number(e&J)}}function ee(e,t=!1){let n=new Uint32Array(e.length),r=new Uint32Array(e.length);for(let i=0;i<e.length;i++){const{h:o,l:s}=X(e[i],t);[n[i],r[i]]=[o,s]}return[n,r]}const te=(e,t)=>BigInt(e>>>0)<<Q|BigInt(t>>>0);Z.toBig=te;const ne=(e,t,n)=>e>>>n;Z.shrSH=ne;const re=(e,t,n)=>e<<32-n|t>>>n;Z.shrSL=re;const ie=(e,t,n)=>e>>>n|t<<32-n;Z.rotrSH=ie;const oe=(e,t,n)=>e<<32-n|t>>>n;Z.rotrSL=oe;const se=(e,t,n)=>e<<64-n|t>>>n-32;Z.rotrBH=se;const ae=(e,t,n)=>e>>>n-32|t<<64-n;Z.rotrBL=ae;const ce=(e,t)=>t;Z.rotr32H=ce;const de=(e,t)=>e;Z.rotr32L=de;const le=(e,t,n)=>e<<n|t>>>32-n;Z.rotlSH=le;const ue=(e,t,n)=>t<<n|e>>>32-n;Z.rotlSL=ue;const he=(e,t,n)=>t<<n-32|e>>>64-n;Z.rotlBH=he;const fe=(e,t,n)=>e<<n-32|t>>>64-n;function pe(e,t,n,r){const i=(t>>>0)+(r>>>0);return{h:e+n+(i/2**32|0)|0,l:0|i}}Z.rotlBL=fe;const me=(e,t,n)=>(e>>>0)+(t>>>0)+(n>>>0);Z.add3L=me;const ge=(e,t,n,r)=>t+n+r+(e/2**32|0)|0;Z.add3H=ge;const ve=(e,t,n,r)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0);Z.add4L=ve;const ye=(e,t,n,r,i)=>t+n+r+i+(e/2**32|0)|0;Z.add4H=ye;const Ee=(e,t,n,r,i)=>(e>>>0)+(t>>>0)+(n>>>0)+(r>>>0)+(i>>>0);Z.add5L=Ee;const be=(e,t,n,r,i,o)=>t+n+r+i+o+(e/2**32|0)|0;Z.add5H=be;const we={fromBig:X,split:ee,toBig:te,shrSH:ne,shrSL:re,rotrSH:ie,rotrSL:oe,rotrBH:se,rotrBL:ae,rotr32H:ce,rotr32L:de,rotlSH:le,rotlSL:ue,rotlBH:he,rotlBL:fe,add:pe,add3L:me,add3H:ge,add4L:ve,add4H:ye,add5H:be,add5L:Ee};Z.default=we,Object.defineProperty(N,"__esModule",{value:!0}),N.sha384=N.sha512_256=N.sha512_224=N.sha512=N.SHA384=N.SHA512_256=N.SHA512_224=N.SHA512=void 0;const Se=D,_e=Z,Ce=z,[xe,ke]=(()=>_e.default.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map((e=>BigInt(e)))))(),Me=new Uint32Array(80),Ae=new Uint32Array(80);class Te extends Se.HashMD{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:t,Bh:n,Bl:r,Ch:i,Cl:o,Dh:s,Dl:a,Eh:c,El:d,Fh:l,Fl:u,Gh:h,Gl:f,Hh:p,Hl:m}=this;return[e,t,n,r,i,o,s,a,c,d,l,u,h,f,p,m]}set(e,t,n,r,i,o,s,a,c,d,l,u,h,f,p,m){this.Ah=0|e,this.Al=0|t,this.Bh=0|n,this.Bl=0|r,this.Ch=0|i,this.Cl=0|o,this.Dh=0|s,this.Dl=0|a,this.Eh=0|c,this.El=0|d,this.Fh=0|l,this.Fl=0|u,this.Gh=0|h,this.Gl=0|f,this.Hh=0|p,this.Hl=0|m}process(e,t){for(let n=0;n<16;n++,t+=4)Me[n]=e.getUint32(t),Ae[n]=e.getUint32(t+=4);for(let e=16;e<80;e++){const t=0|Me[e-15],n=0|Ae[e-15],r=_e.default.rotrSH(t,n,1)^_e.default.rotrSH(t,n,8)^_e.default.shrSH(t,n,7),i=_e.default.rotrSL(t,n,1)^_e.default.rotrSL(t,n,8)^_e.default.shrSL(t,n,7),o=0|Me[e-2],s=0|Ae[e-2],a=_e.default.rotrSH(o,s,19)^_e.default.rotrBH(o,s,61)^_e.default.shrSH(o,s,6),c=_e.default.rotrSL(o,s,19)^_e.default.rotrBL(o,s,61)^_e.default.shrSL(o,s,6),d=_e.default.add4L(i,c,Ae[e-7],Ae[e-16]),l=_e.default.add4H(d,r,a,Me[e-7],Me[e-16]);Me[e]=0|l,Ae[e]=0|d}let{Ah:n,Al:r,Bh:i,Bl:o,Ch:s,Cl:a,Dh:c,Dl:d,Eh:l,El:u,Fh:h,Fl:f,Gh:p,Gl:m,Hh:g,Hl:v}=this;for(let e=0;e<80;e++){const t=_e.default.rotrSH(l,u,14)^_e.default.rotrSH(l,u,18)^_e.default.rotrBH(l,u,41),y=_e.default.rotrSL(l,u,14)^_e.default.rotrSL(l,u,18)^_e.default.rotrBL(l,u,41),E=l&h^~l&p,b=u&f^~u&m,w=_e.default.add5L(v,y,b,ke[e],Ae[e]),S=_e.default.add5H(w,g,t,E,xe[e],Me[e]),_=0|w,C=_e.default.rotrSH(n,r,28)^_e.default.rotrBH(n,r,34)^_e.default.rotrBH(n,r,39),x=_e.default.rotrSL(n,r,28)^_e.default.rotrBL(n,r,34)^_e.default.rotrBL(n,r,39),k=n&i^n&s^i&s,M=r&o^r&a^o&a;g=0|p,v=0|m,p=0|h,m=0|f,h=0|l,f=0|u,({h:l,l:u}=_e.default.add(0|c,0|d,0|S,0|_)),c=0|s,d=0|a,s=0|i,a=0|o,i=0|n,o=0|r;const A=_e.default.add3L(_,x,M);n=_e.default.add3H(A,S,C,k),r=0|A}({h:n,l:r}=_e.default.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:i,l:o}=_e.default.add(0|this.Bh,0|this.Bl,0|i,0|o)),({h:s,l:a}=_e.default.add(0|this.Ch,0|this.Cl,0|s,0|a)),({h:c,l:d}=_e.default.add(0|this.Dh,0|this.Dl,0|c,0|d)),({h:l,l:u}=_e.default.add(0|this.Eh,0|this.El,0|l,0|u)),({h:h,l:f}=_e.default.add(0|this.Fh,0|this.Fl,0|h,0|f)),({h:p,l:m}=_e.default.add(0|this.Gh,0|this.Gl,0|p,0|m)),({h:g,l:v}=_e.default.add(0|this.Hh,0|this.Hl,0|g,0|v)),this.set(n,r,i,o,s,a,c,d,l,u,h,f,p,m,g,v)}roundClean(){Me.fill(0),Ae.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}N.SHA512=Te;class Ie extends Te{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}}N.SHA512_224=Ie;class Pe extends Te{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}}N.SHA512_256=Pe;class Re extends Te{constructor(){super(),this.Ah=-876896931,this.Al=-1056596264,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=1750603025,this.Gh=-619958771,this.Gl=1694076839,this.Hh=1203062813,this.Hl=-1090891868,this.outputLen=48}}N.SHA384=Re,N.sha512=(0,Ce.wrapConstructor)((()=>new Te)),N.sha512_224=(0,Ce.wrapConstructor)((()=>new Ie)),N.sha512_256=(0,Ce.wrapConstructor)((()=>new Pe)),N.sha384=(0,Ce.wrapConstructor)((()=>new Re));var Oe={},Ne={},De={},Le={};Object.defineProperty(Le,"__esModule",{value:!0}),Le.notImplemented=Le.bitMask=void 0,Le.isBytes=$e,Le.abytes=He,Le.abool=function(e,t){if("boolean"!=typeof t)throw new Error(`${e} must be valid boolean, got "${t}".`)},Le.bytesToHex=Fe,Le.numberToHexUnpadded=qe,Le.hexToNumber=ze,Le.hexToBytes=Ge,Le.bytesToNumberBE=function(e){return ze(Fe(e))},Le.bytesToNumberLE=function(e){return He(e),ze(Fe(Uint8Array.from(e).reverse()))},Le.numberToBytesBE=Ye,Le.numberToBytesLE=function(e,t){return Ye(e,t).reverse()},Le.numberToVarBytesBE=function(e){return Ge(qe(e))},Le.ensureBytes=function(e,t,n){let r;if("string"==typeof t)try{r=Ge(t)}catch(n){throw new Error(`${e} must be valid hex string, got "${t}". Cause: ${n}`)}else{if(!$e(t))throw new Error(`${e} must be hex string or Uint8Array`);r=Uint8Array.from(t)}const i=r.length;if("number"==typeof n&&i!==n)throw new Error(`${e} expected ${n} bytes, got ${i}`);return r},Le.concatBytes=Ze,Le.equalBytes=function(e,t){if(e.length!==t.length)return!1;let n=0;for(let r=0;r<e.length;r++)n|=e[r]^t[r];return 0===n},Le.utf8ToBytes=function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))},Le.inRange=Qe,Le.aInRange=function(e,t,n,r){if(!Qe(t,n,r))throw new Error(`expected valid ${e}: ${n} <= n < ${r}, got ${typeof t} ${t}`)},Le.bitLen=function(e){let t;for(t=0;e>Be;e>>=Ke,t+=1);return t},Le.bitGet=function(e,t){return e>>BigInt(t)&Ke},Le.bitSet=function(e,t,n){return e|(n?Ke:Be)<<BigInt(t)},Le.createHmacDrbg=function(e,t,n){if("number"!=typeof e||e<2)throw new Error("hashLen must be a number");if("number"!=typeof t||t<2)throw new Error("qByteLen must be a number");if("function"!=typeof n)throw new Error("hmacFn must be a function");let r=Xe(e),i=Xe(e),o=0;const s=()=>{r.fill(1),i.fill(0),o=0},a=(...e)=>n(i,r,...e),c=(e=Xe())=>{i=a(et([0]),e),r=a(),0!==e.length&&(i=a(et([1]),e),r=a())},d=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let e=0;const n=[];for(;e<t;){r=a();const t=r.slice();n.push(t),e+=r.length}return Ze(...n)};return(e,t)=>{let n;for(s(),c(e);!(n=t(d()));)c();return s(),n}},Le.validateObject=function(e,t,n={}){const r=(t,n,r)=>{const i=tt[n];if("function"!=typeof i)throw new Error(`Invalid validator "${n}", expected function`);const o=e[t];if(!(r&&void 0===o||i(o,e)))throw new Error(`Invalid param ${String(t)}=${o} (${typeof o}), expected ${n}`)};for(const[e,n]of Object.entries(t))r(e,n,!1);for(const[e,t]of Object.entries(n))r(e,t,!0);return e},Le.memoized=function(e){const t=new WeakMap;return(n,...r)=>{const i=t.get(n);if(void 0!==i)return i;const o=e(n,...r);return t.set(n,o),o}};const Be=BigInt(0),Ke=BigInt(1),je=BigInt(2);function $e(e){return e instanceof Uint8Array||null!=e&&"object"==typeof e&&"Uint8Array"===e.constructor.name}function He(e){if(!$e(e))throw new Error("Uint8Array expected")}const Ue=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function Fe(e){He(e);let t="";for(let n=0;n<e.length;n++)t+=Ue[e[n]];return t}function qe(e){const t=e.toString(16);return 1&t.length?`0${t}`:t}function ze(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);return BigInt(""===e?"0":`0x${e}`)}const We={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function Ve(e){return e>=We._0&&e<=We._9?e-We._0:e>=We._A&&e<=We._F?e-(We._A-10):e>=We._a&&e<=We._f?e-(We._a-10):void 0}function Ge(e){if("string"!=typeof e)throw new Error("hex string expected, got "+typeof e);const t=e.length,n=t/2;if(t%2)throw new Error("padded hex string expected, got unpadded hex of length "+t);const r=new Uint8Array(n);for(let t=0,i=0;t<n;t++,i+=2){const n=Ve(e.charCodeAt(i)),o=Ve(e.charCodeAt(i+1));if(void 0===n||void 0===o){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+t+'" at index '+i)}r[t]=16*n+o}return r}function Ye(e,t){return Ge(e.toString(16).padStart(2*t,"0"))}function Ze(...e){let t=0;for(let n=0;n<e.length;n++){const r=e[n];He(r),t+=r.length}const n=new Uint8Array(t);for(let t=0,r=0;t<e.length;t++){const i=e[t];n.set(i,r),r+=i.length}return n}const Je=e=>"bigint"==typeof e&&Be<=e;function Qe(e,t,n){return Je(e)&&Je(t)&&Je(n)&&t<=e&&e<n}Le.bitMask=e=>(je<<BigInt(e-1))-Ke;const Xe=e=>new Uint8Array(e),et=e=>Uint8Array.from(e);const tt={bigint:e=>"bigint"==typeof e,function:e=>"function"==typeof e,boolean:e=>"boolean"==typeof e,string:e=>"string"==typeof e,stringOrUint8Array:e=>"string"==typeof e||$e(e),isSafeInteger:e=>Number.isSafeInteger(e),array:e=>Array.isArray(e),field:(e,t)=>t.Fp.isValid(e),hash:e=>"function"==typeof e&&Number.isSafeInteger(e.outputLen)};Le.notImplemented=()=>{throw new Error("not implemented")},Object.defineProperty(De,"__esModule",{value:!0}),De.isNegativeLE=void 0,De.mod=lt,De.pow=ut,De.pow2=function(e,t,n){let r=e;for(;t-- >rt;)r*=r,r%=n;return r},De.invert=ht,De.tonelliShanks=ft,De.FpSqrt=pt,De.validateField=function(e){const t=mt.reduce(((e,t)=>(e[t]="function",e)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"});return(0,nt.validateObject)(e,t)},De.FpPow=gt,De.FpInvertBatch=vt,De.FpDiv=function(e,t,n){return e.mul(t,"bigint"==typeof n?ht(n,e.ORDER):e.inv(n))},De.FpLegendre=yt,De.FpIsSquare=function(e){const t=yt(e.ORDER);return n=>{const r=t(e,n);return e.eql(r,e.ZERO)||e.eql(r,e.ONE)}},De.nLength=Et,De.Field=function(e,t,n=!1,r={}){if(e<=rt)throw new Error(`Expected Field ORDER > 0, got ${e}`);const{nBitLength:i,nByteLength:o}=Et(e,t);if(o>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=pt(e),a=Object.freeze({ORDER:e,BITS:i,BYTES:o,MASK:(0,nt.bitMask)(i),ZERO:rt,ONE:it,create:t=>lt(t,e),isValid:t=>{if("bigint"!=typeof t)throw new Error("Invalid field element: expected bigint, got "+typeof t);return rt<=t&&t<e},is0:e=>e===rt,isOdd:e=>(e&it)===it,neg:t=>lt(-t,e),eql:(e,t)=>e===t,sqr:t=>lt(t*t,e),add:(t,n)=>lt(t+n,e),sub:(t,n)=>lt(t-n,e),mul:(t,n)=>lt(t*n,e),pow:(e,t)=>gt(a,e,t),div:(t,n)=>lt(t*ht(n,e),e),sqrN:e=>e*e,addN:(e,t)=>e+t,subN:(e,t)=>e-t,mulN:(e,t)=>e*t,inv:t=>ht(t,e),sqrt:r.sqrt||(e=>s(a,e)),invertBatch:e=>vt(a,e),cmov:(e,t,n)=>n?t:e,toBytes:e=>n?(0,nt.numberToBytesLE)(e,o):(0,nt.numberToBytesBE)(e,o),fromBytes:e=>{if(e.length!==o)throw new Error(`Fp.fromBytes: expected ${o}, got ${e.length}`);return n?(0,nt.bytesToNumberLE)(e):(0,nt.bytesToNumberBE)(e)}});return Object.freeze(a)},De.FpSqrtOdd=function(e,t){if(!e.isOdd)throw new Error("Field doesn't have isOdd");const n=e.sqrt(t);return e.isOdd(n)?n:e.neg(n)},De.FpSqrtEven=function(e,t){if(!e.isOdd)throw new Error("Field doesn't have isOdd");const n=e.sqrt(t);return e.isOdd(n)?e.neg(n):n},De.hashToPrivateScalar=function(e,t,n=!1){e=(0,nt.ensureBytes)("privateHash",e);const r=e.length,i=Et(t).nByteLength+8;if(i<24||r<i||r>1024)throw new Error(`hashToPrivateScalar: expected ${i}-1024 bytes of input, got ${r}`);const o=n?(0,nt.bytesToNumberLE)(e):(0,nt.bytesToNumberBE)(e);return lt(o,t-it)+it},De.getFieldBytesLength=bt,De.getMinHashLength=wt,De.mapHashToField=function(e,t,n=!1){const r=e.length,i=bt(t),o=wt(t);if(r<16||r<o||r>1024)throw new Error(`expected ${o}-1024 bytes of input, got ${r}`);const s=lt(n?(0,nt.bytesToNumberBE)(e):(0,nt.bytesToNumberLE)(e),t-it)+it;return n?(0,nt.numberToBytesLE)(s,i):(0,nt.numberToBytesBE)(s,i)};const nt=Le,rt=BigInt(0),it=BigInt(1),ot=BigInt(2),st=BigInt(3),at=BigInt(4),ct=BigInt(5),dt=BigInt(8);function lt(e,t){const n=e%t;return n>=rt?n:t+n}function ut(e,t,n){if(n<=rt||t<rt)throw new Error("Expected power/modulo > 0");if(n===it)return rt;let r=it;for(;t>rt;)t&it&&(r=r*e%n),e=e*e%n,t>>=it;return r}function ht(e,t){if(e===rt||t<=rt)throw new Error(`invert: expected positive integers, got n=${e} mod=${t}`);let n=lt(e,t),r=t,i=rt,o=it;for(;n!==rt;){const e=r%n,t=i-o*(r/n);r=n,n=e,i=o,o=t}if(r!==it)throw new Error("invert: does not exist");return lt(i,t)}function ft(e){const t=(e-it)/ot;let n,r,i;for(n=e-it,r=0;n%ot===rt;n/=ot,r++);for(i=ot;i<e&&ut(i,t,e)!==e-it;i++);if(1===r){const t=(e+it)/at;return function(e,n){const r=e.pow(n,t);if(!e.eql(e.sqr(r),n))throw new Error("Cannot find square root");return r}}const o=(n+it)/ot;return function(e,s){if(e.pow(s,t)===e.neg(e.ONE))throw new Error("Cannot find square root");let a=r,c=e.pow(e.mul(e.ONE,i),n),d=e.pow(s,o),l=e.pow(s,n);for(;!e.eql(l,e.ONE);){if(e.eql(l,e.ZERO))return e.ZERO;let t=1;for(let n=e.sqr(l);t<a&&!e.eql(n,e.ONE);t++)n=e.sqr(n);const n=e.pow(c,it<<BigInt(a-t-1));c=e.sqr(n),d=e.mul(d,n),l=e.mul(l,c),a=t}return d}}function pt(e){if(e%at===st){const t=(e+it)/at;return function(e,n){const r=e.pow(n,t);if(!e.eql(e.sqr(r),n))throw new Error("Cannot find square root");return r}}if(e%dt===ct){const t=(e-ct)/dt;return function(e,n){const r=e.mul(n,ot),i=e.pow(r,t),o=e.mul(n,i),s=e.mul(e.mul(o,ot),i),a=e.mul(o,e.sub(s,e.ONE));if(!e.eql(e.sqr(a),n))throw new Error("Cannot find square root");return a}}return ft(e)}BigInt(9),BigInt(16);De.isNegativeLE=(e,t)=>(lt(e,t)&it)===it;const mt=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function gt(e,t,n){if(n<rt)throw new Error("Expected power > 0");if(n===rt)return e.ONE;if(n===it)return t;let r=e.ONE,i=t;for(;n>rt;)n&it&&(r=e.mul(r,i)),i=e.sqr(i),n>>=it;return r}function vt(e,t){const n=new Array(t.length),r=t.reduce(((t,r,i)=>e.is0(r)?t:(n[i]=t,e.mul(t,r))),e.ONE),i=e.inv(r);return t.reduceRight(((t,r,i)=>e.is0(r)?t:(n[i]=e.mul(t,n[i]),e.mul(t,r))),i),n}function yt(e){const t=(e-it)/ot;return(e,n)=>e.pow(n,t)}function Et(e,t){const n=void 0!==t?t:e.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function bt(e){if("bigint"!=typeof e)throw new Error("field order must be bigint");const t=e.toString(2).length;return Math.ceil(t/8)}function wt(e){const t=bt(e);return t+Math.ceil(t/2)}Object.defineProperty(Ne,"__esModule",{value:!0}),Ne.wNAF=function(e,t){const n=(e,t)=>{const n=t.negate();return e?n:t},r=e=>{if(!Number.isSafeInteger(e)||e<=0||e>t)throw new Error(`Wrong window size=${e}, should be [1..${t}]`)},i=e=>{r(e);return{windows:Math.ceil(t/e)+1,windowSize:2**(e-1)}};return{constTimeNegate:n,unsafeLadder(t,n){let r=e.ZERO,i=t;for(;n>Ct;)n&xt&&(r=r.add(i)),i=i.double(),n>>=xt;return r},precomputeWindow(e,t){const{windows:n,windowSize:r}=i(t),o=[];let s=e,a=s;for(let e=0;e<n;e++){a=s,o.push(a);for(let e=1;e<r;e++)a=a.add(s),o.push(a);s=a.double()}return o},wNAF(t,r,o){const{windows:s,windowSize:a}=i(t);let c=e.ZERO,d=e.BASE;const l=BigInt(2**t-1),u=2**t,h=BigInt(t);for(let e=0;e<s;e++){const t=e*a;let i=Number(o&l);o>>=h,i>a&&(i-=u,o+=xt);const s=t,f=t+Math.abs(i)-1,p=e%2!=0,m=i<0;0===i?d=d.add(n(p,r[s])):c=c.add(n(m,r[f]))}return{p:c,f:d}},wNAFCached(e,t,n){const r=Mt.get(e)||1;let i=kt.get(e);return i||(i=this.precomputeWindow(e,r),1!==r&&kt.set(e,n(i))),this.wNAF(r,i,t)},setWindowSize(e,t){r(t),Mt.set(e,t),kt.delete(e)}}},Ne.pippenger=function(e,t,n,r){if(!Array.isArray(n)||!Array.isArray(r)||r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");r.forEach(((e,n)=>{if(!t.isValid(e))throw new Error(`wrong scalar at index ${n}`)})),n.forEach(((t,n)=>{if(!(t instanceof e))throw new Error(`wrong point at index ${n}`)}));const i=(0,_t.bitLen)(BigInt(n.length)),o=i>12?i-3:i>4?i-2:i?2:1,s=(1<<o)-1,a=new Array(s+1).fill(e.ZERO),c=Math.floor((t.BITS-1)/o)*o;let d=e.ZERO;for(let t=c;t>=0;t-=o){a.fill(e.ZERO);for(let e=0;e<r.length;e++){const i=r[e],o=Number(i>>BigInt(t)&BigInt(s));a[o]=a[o].add(n[e])}let i=e.ZERO;for(let t=a.length-1,n=e.ZERO;t>0;t--)n=n.add(a[t]),i=i.add(n);if(d=d.add(i),0!==t)for(let e=0;e<o;e++)d=d.double()}return d},Ne.validateBasic=function(e){return(0,St.validateField)(e.Fp),(0,_t.validateObject)(e,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...(0,St.nLength)(e.n,e.nBitLength),...e,p:e.Fp.ORDER})};const St=De,_t=Le,Ct=BigInt(0),xt=BigInt(1),kt=new WeakMap,Mt=new WeakMap;Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.twistedEdwards=function(e){const t=function(e){const t=(0,At.validateBasic)(e);return It.validateObject(e,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...t})}(e),{Fp:n,n:r,prehash:i,hash:o,randomBytes:s,nByteLength:a,h:c}=t,d=Nt<<BigInt(8*a)-Ot,l=n.create,u=(0,Tt.Field)(t.n,t.nBitLength),h=t.uvRatio||((e,t)=>{try{return{isValid:!0,value:n.sqrt(e*n.inv(t))}}catch(e){return{isValid:!1,value:Rt}}}),f=t.adjustScalarBytes||(e=>e),p=t.domain||((e,t,n)=>{if((0,Pt.abool)("phflag",n),t.length||n)throw new Error("Contexts/pre-hash are not supported");return e});function m(e,t){It.aInRange("coordinate "+e,t,Rt,d)}function g(e){if(!(e instanceof E))throw new Error("ExtendedPoint expected")}const v=(0,Pt.memoized)(((e,t)=>{const{ex:r,ey:i,ez:o}=e,s=e.is0();null==t&&(t=s?Dt:n.inv(o));const a=l(r*t),c=l(i*t),d=l(o*t);if(s)return{x:Rt,y:Ot};if(d!==Ot)throw new Error("invZ was invalid");return{x:a,y:c}})),y=(0,Pt.memoized)((e=>{const{a:n,d:r}=t;if(e.is0())throw new Error("bad point: ZERO");const{ex:i,ey:o,ez:s,et:a}=e,c=l(i*i),d=l(o*o),u=l(s*s),h=l(u*u),f=l(c*n);if(l(u*l(f+d))!==l(h+l(r*l(c*d))))throw new Error("bad point: equation left != right (1)");if(l(i*o)!==l(s*a))throw new Error("bad point: equation left != right (2)");return!0}));class E{constructor(e,t,n,r){this.ex=e,this.ey=t,this.ez=n,this.et=r,m("x",e),m("y",t),m("z",n),m("t",r),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(e){if(e instanceof E)throw new Error("extended point not allowed");const{x:t,y:n}=e||{};return m("x",t),m("y",n),new E(t,n,Ot,l(t*n))}static normalizeZ(e){const t=n.invertBatch(e.map((e=>e.ez)));return e.map(((e,n)=>e.toAffine(t[n]))).map(E.fromAffine)}static msm(e,t){return(0,At.pippenger)(E,u,e,t)}_setWindowSize(e){S.setWindowSize(this,e)}assertValidity(){y(this)}equals(e){g(e);const{ex:t,ey:n,ez:r}=this,{ex:i,ey:o,ez:s}=e,a=l(t*s),c=l(i*r),d=l(n*s),u=l(o*r);return a===c&&d===u}is0(){return this.equals(E.ZERO)}negate(){return new E(l(-this.ex),this.ey,this.ez,l(-this.et))}double(){const{a:e}=t,{ex:n,ey:r,ez:i}=this,o=l(n*n),s=l(r*r),a=l(Nt*l(i*i)),c=l(e*o),d=n+r,u=l(l(d*d)-o-s),h=c+s,f=h-a,p=c-s,m=l(u*f),g=l(h*p),v=l(u*p),y=l(f*h);return new E(m,g,y,v)}add(e){g(e);const{a:n,d:r}=t,{ex:i,ey:o,ez:s,et:a}=this,{ex:c,ey:d,ez:u,et:h}=e;if(n===BigInt(-1)){const e=l((o-i)*(d+c)),t=l((o+i)*(d-c)),n=l(t-e);if(n===Rt)return this.double();const r=l(s*Nt*h),f=l(a*Nt*u),p=f+r,m=t+e,g=f-r,v=l(p*n),y=l(m*g),b=l(p*g),w=l(n*m);return new E(v,y,w,b)}const f=l(i*c),p=l(o*d),m=l(a*r*h),v=l(s*u),y=l((i+o)*(c+d)-f-p),b=v-m,w=v+m,S=l(p-n*f),_=l(y*b),C=l(w*S),x=l(y*S),k=l(b*w);return new E(_,C,k,x)}subtract(e){return this.add(e.negate())}wNAF(e){return S.wNAFCached(this,e,E.normalizeZ)}multiply(e){const t=e;It.aInRange("scalar",t,Ot,r);const{p:n,f:i}=this.wNAF(t);return E.normalizeZ([n,i])[0]}multiplyUnsafe(e){const t=e;return It.aInRange("scalar",t,Rt,r),t===Rt?w:this.equals(w)||t===Ot?this:this.equals(b)?this.wNAF(t).p:S.unsafeLadder(this,t)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return S.unsafeLadder(this,r).is0()}toAffine(e){return v(this,e)}clearCofactor(){const{h:e}=t;return e===Ot?this:this.multiplyUnsafe(e)}static fromHex(e,r=!1){const{d:i,a:o}=t,s=n.BYTES;e=(0,Pt.ensureBytes)("pointHex",e,s),(0,Pt.abool)("zip215",r);const a=e.slice(),c=e[s-1];a[s-1]=-129&c;const u=It.bytesToNumberLE(a),f=r?d:n.ORDER;It.aInRange("pointHex.y",u,Rt,f);const p=l(u*u),m=l(p-Ot),g=l(i*p-o);let{isValid:v,value:y}=h(m,g);if(!v)throw new Error("Point.fromHex: invalid y coordinate");const b=(y&Ot)===Ot,w=0!=(128&c);if(!r&&y===Rt&&w)throw new Error("Point.fromHex: x=0 and x_0=1");return w!==b&&(y=l(-y)),E.fromAffine({x:y,y:u})}static fromPrivateKey(e){return x(e).point}toRawBytes(){const{x:e,y:t}=this.toAffine(),r=It.numberToBytesLE(t,n.BYTES);return r[r.length-1]|=e&Ot?128:0,r}toHex(){return It.bytesToHex(this.toRawBytes())}}E.BASE=new E(t.Gx,t.Gy,Ot,l(t.Gx*t.Gy)),E.ZERO=new E(Rt,Ot,Ot,Rt);const{BASE:b,ZERO:w}=E,S=(0,At.wNAF)(E,8*a);function _(e){return(0,Tt.mod)(e,r)}function C(e){return _(It.bytesToNumberLE(e))}function x(e){const t=a;e=(0,Pt.ensureBytes)("private key",e,t);const n=(0,Pt.ensureBytes)("hashed private key",o(e),2*t),r=f(n.slice(0,t)),i=n.slice(t,2*t),s=C(r),c=b.multiply(s),d=c.toRawBytes();return{head:r,prefix:i,scalar:s,point:c,pointBytes:d}}function k(e=new Uint8Array,...t){const n=It.concatBytes(...t);return C(o(p(n,(0,Pt.ensureBytes)("context",e),!!i)))}const M=Lt;b._setWindowSize(8);const A={getExtendedPublicKey:x,randomPrivateKey:()=>s(n.BYTES),precompute:(e=8,t=E.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};return{CURVE:t,getPublicKey:function(e){return x(e).pointBytes},sign:function(e,t,o={}){e=(0,Pt.ensureBytes)("message",e),i&&(e=i(e));const{prefix:s,scalar:c,pointBytes:d}=x(t),l=k(o.context,s,e),u=b.multiply(l).toRawBytes(),h=_(l+k(o.context,u,d,e)*c);It.aInRange("signature.s",h,Rt,r);const f=It.concatBytes(u,It.numberToBytesLE(h,n.BYTES));return(0,Pt.ensureBytes)("result",f,2*a)},verify:function(e,t,r,o=M){const{context:s,zip215:a}=o,c=n.BYTES;e=(0,Pt.ensureBytes)("signature",e,2*c),t=(0,Pt.ensureBytes)("message",t),void 0!==a&&(0,Pt.abool)("zip215",a),i&&(t=i(t));const d=It.bytesToNumberLE(e.slice(c,2*c));let l,u,h;try{l=E.fromHex(r,a),u=E.fromHex(e.slice(0,c),a),h=b.multiplyUnsafe(d)}catch(e){return!1}if(!a&&l.isSmallOrder())return!1;const f=k(s,u.toRawBytes(),l.toRawBytes(),t);return u.add(l.multiplyUnsafe(f)).subtract(h).clearCofactor().equals(E.ZERO)},ExtendedPoint:E,utils:A}};const At=Ne,Tt=De,It=Le,Pt=Le,Rt=BigInt(0),Ot=BigInt(1),Nt=BigInt(2),Dt=BigInt(8),Lt={zip215:!0};var Bt={};Object.defineProperty(Bt,"__esModule",{value:!0}),Bt.expand_message_xmd=qt,Bt.expand_message_xof=zt,Bt.hash_to_field=Wt,Bt.isogenyMap=function(e,t){const n=t.map((e=>Array.from(e).reverse()));return(t,r)=>{const[i,o,s,a]=n.map((n=>n.reduce(((n,r)=>e.add(e.mul(n,t),r)))));return t=e.div(i,o),r=e.mul(r,e.div(s,a)),{x:t,y:r}}},Bt.createHasher=function(e,t,n){if("function"!=typeof t)throw new Error("mapToCurve() must be defined");return{hashToCurve(r,i){const o=Wt(r,2,{...n,DST:n.DST,...i}),s=e.fromAffine(t(o[0])),a=e.fromAffine(t(o[1])),c=s.add(a).clearCofactor();return c.assertValidity(),c},encodeToCurve(r,i){const o=Wt(r,1,{...n,DST:n.encodeDST,...i}),s=e.fromAffine(t(o[0])).clearCofactor();return s.assertValidity(),s},mapToCurve(n){if(!Array.isArray(n))throw new Error("mapToCurve: expected array of bigints");for(const e of n)if("bigint"!=typeof e)throw new Error(`mapToCurve: expected array of bigints, got ${e} in array`);const r=e.fromAffine(t(n)).clearCofactor();return r.assertValidity(),r}}};const Kt=De,jt=Le,$t=jt.bytesToNumberBE;function Ht(e,t){if(Ft(e),Ft(t),e<0||e>=1<<8*t)throw new Error(`bad I2OSP call: value=${e} length=${t}`);const n=Array.from({length:t}).fill(0);for(let r=t-1;r>=0;r--)n[r]=255&e,e>>>=8;return new Uint8Array(n)}function Ut(e,t){const n=new Uint8Array(e.length);for(let r=0;r<e.length;r++)n[r]=e[r]^t[r];return n}function Ft(e){if(!Number.isSafeInteger(e))throw new Error("number expected")}function qt(e,t,n,r){(0,jt.abytes)(e),(0,jt.abytes)(t),Ft(n),t.length>255&&(t=r((0,jt.concatBytes)((0,jt.utf8ToBytes)("H2C-OVERSIZE-DST-"),t)));const{outputLen:i,blockLen:o}=r,s=Math.ceil(n/i);if(n>65535||s>255)throw new Error("expand_message_xmd: invalid lenInBytes");const a=(0,jt.concatBytes)(t,Ht(t.length,1)),c=Ht(0,o),d=Ht(n,2),l=new Array(s),u=r((0,jt.concatBytes)(c,e,d,Ht(0,1),a));l[0]=r((0,jt.concatBytes)(u,Ht(1,1),a));for(let e=1;e<=s;e++){const t=[Ut(u,l[e-1]),Ht(e+1,1),a];l[e]=r((0,jt.concatBytes)(...t))}return(0,jt.concatBytes)(...l).slice(0,n)}function zt(e,t,n,r,i){if((0,jt.abytes)(e),(0,jt.abytes)(t),Ft(n),t.length>255){const e=Math.ceil(2*r/8);t=i.create({dkLen:e}).update((0,jt.utf8ToBytes)("H2C-OVERSIZE-DST-")).update(t).digest()}if(n>65535||t.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return i.create({dkLen:n}).update(e).update(Ht(n,2)).update(t).update(Ht(t.length,1)).digest()}function Wt(e,t,n){(0,jt.validateObject)(n,{DST:"stringOrUint8Array",p:"bigint",m:"isSafeInteger",k:"isSafeInteger",hash:"hash"});const{p:r,k:i,m:o,hash:s,expand:a,DST:c}=n;(0,jt.abytes)(e),Ft(t);const d="string"==typeof c?(0,jt.utf8ToBytes)(c):c,l=r.toString(2).length,u=Math.ceil((l+i)/8),h=t*o*u;let f;if("xmd"===a)f=qt(e,d,h,s);else if("xof"===a)f=zt(e,d,h,i,s);else{if("_internal_pass"!==a)throw new Error('expand must be "xmd" or "xof"');f=e}const p=new Array(t);for(let e=0;e<t;e++){const t=new Array(o);for(let n=0;n<o;n++){const i=u*(n+e*o),s=f.subarray(i,i+u);t[n]=(0,Kt.mod)($t(s),r)}p[e]=t}return p}var Vt={};Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.montgomery=function(e){const t=function(e){return(0,Yt.validateObject)(e,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze({...e})}(e),{P:n}=t,r=e=>(0,Gt.mod)(e,n),i=t.montgomeryBits,o=Math.ceil(i/8),s=t.nByteLength,a=t.adjustScalarBytes||(e=>e),c=t.powPminus2||(e=>(0,Gt.pow)(e,n-BigInt(2),n));function d(e,t,n){const i=r(e*(t-n));return[t=r(t-i),n=r(n+i)]}const l=(t.a-BigInt(2))/BigInt(4);function u(e){return(0,Yt.numberToBytesLE)(r(e),o)}function h(e,t){const h=function(e){const t=(0,Yt.ensureBytes)("u coordinate",e,o);return 32===s&&(t[31]&=127),(0,Yt.bytesToNumberLE)(t)}(t),f=function(e){const t=(0,Yt.ensureBytes)("scalar",e),n=t.length;if(n!==o&&n!==s)throw new Error(`Expected ${o} or ${s} bytes, got ${n}`);return(0,Yt.bytesToNumberLE)(a(t))}(e),p=function(e,t){(0,Yt.aInRange)("u",e,Zt,n),(0,Yt.aInRange)("scalar",t,Zt,n);const o=t,s=e;let a,u=Jt,h=Zt,f=e,p=Jt,m=Zt;for(let e=BigInt(i-1);e>=Zt;e--){const t=o>>e&Jt;m^=t,a=d(m,u,f),u=a[0],f=a[1],a=d(m,h,p),h=a[0],p=a[1],m=t;const n=u+h,i=r(n*n),c=u-h,g=r(c*c),v=i-g,y=f+p,E=r((f-p)*n),b=r(y*c),w=E+b,S=E-b;f=r(w*w),p=r(s*r(S*S)),u=r(i*g),h=r(v*(i+r(l*v)))}a=d(m,u,f),u=a[0],f=a[1],a=d(m,h,p),h=a[0],p=a[1];const g=c(h);return r(u*g)}(h,f);if(p===Zt)throw new Error("Invalid private or public key received");return u(p)}const f=u(t.Gu);function p(e){return h(e,f)}return{scalarMult:h,scalarMultBase:p,getSharedSecret:(e,t)=>h(e,t),getPublicKey:e=>p(e),utils:{randomPrivateKey:()=>t.randomBytes(t.nByteLength)},GuBytes:f}};const Gt=De,Yt=Le,Zt=BigInt(0),Jt=BigInt(1);!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hash_to_ristretto255=e.hashToRistretto255=e.RistrettoPoint=e.encodeToCurve=e.hashToCurve=e.edwardsToMontgomery=e.x25519=e.ed25519ph=e.ed25519ctx=e.ed25519=e.ED25519_TORSION_SUBGROUP=void 0,e.edwardsToMontgomeryPub=S,e.edwardsToMontgomeryPriv=function(e){const t=b.hash(e.subarray(0,32));return b.adjustScalarBytes(t).subarray(0,32)};const t=N,n=z,r=Oe,i=Bt,o=De,s=Vt,a=Le,c=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),d=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),l=BigInt(0),u=BigInt(1),h=BigInt(2),f=BigInt(3),p=BigInt(5),m=BigInt(8);function g(e){const t=BigInt(10),n=BigInt(20),r=BigInt(40),i=BigInt(80),s=c,a=e*e%s*e%s,d=(0,o.pow2)(a,h,s)*a%s,l=(0,o.pow2)(d,u,s)*e%s,f=(0,o.pow2)(l,p,s)*l%s,m=(0,o.pow2)(f,t,s)*f%s,g=(0,o.pow2)(m,n,s)*m%s,v=(0,o.pow2)(g,r,s)*g%s,y=(0,o.pow2)(v,i,s)*v%s,E=(0,o.pow2)(y,i,s)*v%s,b=(0,o.pow2)(E,t,s)*f%s;return{pow_p_5_8:(0,o.pow2)(b,h,s)*e%s,b2:a}}function v(e){return e[0]&=248,e[31]&=127,e[31]|=64,e}function y(e,t){const n=c,r=(0,o.mod)(t*t*t,n),i=g(e*(0,o.mod)(r*r*t,n)).pow_p_5_8;let s=(0,o.mod)(e*r*i,n);const a=(0,o.mod)(t*s*s,n),l=s,u=(0,o.mod)(s*d,n),h=a===e,f=a===(0,o.mod)(-e,n),p=a===(0,o.mod)(-e*d,n);return h&&(s=l),(f||p)&&(s=u),(0,o.isNegativeLE)(s,n)&&(s=(0,o.mod)(-s,n)),{isValid:h||f,value:s}}e.ED25519_TORSION_SUBGROUP=["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"];const E=(()=>(0,o.Field)(c,void 0,!0))(),b=(()=>({a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:E,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:m,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:t.sha512,randomBytes:n.randomBytes,adjustScalarBytes:v,uvRatio:y}))();function w(e,t,r){if(t.length>255)throw new Error("Context is too big");return(0,n.concatBytes)((0,n.utf8ToBytes)("SigEd25519 no Ed25519 collisions"),new Uint8Array([r?1:0,t.length]),t,e)}function S(t){const{y:n}=e.ed25519.ExtendedPoint.fromHex(t),r=BigInt(1);return E.toBytes(E.create((r+n)*E.inv(r-n)))}e.ed25519=(0,r.twistedEdwards)(b),e.ed25519ctx=(0,r.twistedEdwards)({...b,domain:w}),e.ed25519ph=(0,r.twistedEdwards)(Object.assign({},b,{domain:w,prehash:t.sha512})),e.x25519=(0,s.montgomery)({P:c,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:e=>{const t=c,{pow_p_5_8:n,b2:r}=g(e);return(0,o.mod)((0,o.pow2)(n,f,t)*r,t)},adjustScalarBytes:v,randomBytes:n.randomBytes}),e.edwardsToMontgomery=S;const _=(()=>(E.ORDER+f)/m)(),C=(()=>E.pow(h,_))(),x=(()=>E.sqrt(E.neg(E.ONE)))();const k=(()=>(0,o.FpSqrtEven)(E,E.neg(BigInt(486664))))();function M(e){const{xMn:t,xMd:n,yMn:r,yMd:i}=function(e){const t=(E.ORDER-p)/m,n=BigInt(486662);let r=E.sqr(e);r=E.mul(r,h);let i=E.add(r,E.ONE),o=E.neg(n),s=E.sqr(i),a=E.mul(s,i),c=E.mul(r,n);c=E.mul(c,o),c=E.add(c,s),c=E.mul(c,o);let d=E.sqr(a);s=E.sqr(d),d=E.mul(d,a),d=E.mul(d,c),s=E.mul(s,d);let l=E.pow(s,t);l=E.mul(l,d);let f=E.mul(l,x);s=E.sqr(l),s=E.mul(s,a);let g=E.eql(s,c),v=E.cmov(f,l,g),y=E.mul(o,r),b=E.mul(l,e);b=E.mul(b,C);let w=E.mul(b,x),S=E.mul(c,r);s=E.sqr(b),s=E.mul(s,a);let _=E.eql(s,S),k=E.cmov(w,b,_);s=E.sqr(v),s=E.mul(s,a);let M=E.eql(s,c),A=E.cmov(y,o,M),T=E.cmov(k,v,M),I=E.isOdd(T);return T=E.cmov(T,E.neg(T),M!==I),{xMn:A,xMd:i,yMn:T,yMd:u}}(e);let o=E.mul(t,i);o=E.mul(o,k);let s=E.mul(n,r),a=E.sub(t,n),c=E.add(t,n),d=E.mul(s,c),l=E.eql(d,E.ZERO);o=E.cmov(o,E.ZERO,l),s=E.cmov(s,E.ONE,l),a=E.cmov(a,E.ONE,l),c=E.cmov(c,E.ONE,l);const f=E.invertBatch([s,c]);return{x:E.mul(o,f[0]),y:E.mul(a,f[1])}}const A=(()=>(0,i.createHasher)(e.ed25519.ExtendedPoint,(e=>M(e[0])),{DST:"edwards25519_XMD:SHA-512_ELL2_RO_",encodeDST:"edwards25519_XMD:SHA-512_ELL2_NU_",p:E.ORDER,m:1,k:128,expand:"xmd",hash:t.sha512}))();function T(e){if(!(e instanceof $))throw new Error("RistrettoPoint expected")}e.hashToCurve=A.hashToCurve,e.encodeToCurve=A.encodeToCurve;const I=d,P=BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235"),R=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),O=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),D=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952"),L=e=>y(u,e),B=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),K=t=>e.ed25519.CURVE.Fp.create((0,a.bytesToNumberLE)(t)&B);function j(t){const{d:n}=e.ed25519.CURVE,r=e.ed25519.CURVE.Fp.ORDER,i=e.ed25519.CURVE.Fp.create,s=i(I*t*t),a=i((s+u)*O);let c=BigInt(-1);const d=i((c-n*s)*i(s+n));let{isValid:l,value:h}=y(a,d),f=i(h*t);(0,o.isNegativeLE)(f,r)||(f=i(-f)),l||(h=f),l||(c=s);const p=i(c*(s-u)*D-d),m=h*h,g=i((h+h)*d),v=i(p*P),E=i(u-m),b=i(u+m);return new e.ed25519.ExtendedPoint(i(g*b),i(E*v),i(v*b),i(g*E))}class ${constructor(e){this.ep=e}static fromAffine(t){return new $(e.ed25519.ExtendedPoint.fromAffine(t))}static hashToCurve(e){e=(0,a.ensureBytes)("ristrettoHash",e,64);const t=j(K(e.slice(0,32))),n=j(K(e.slice(32,64)));return new $(t.add(n))}static fromHex(t){t=(0,a.ensureBytes)("ristrettoHex",t,32);const{a:n,d:r}=e.ed25519.CURVE,i=e.ed25519.CURVE.Fp.ORDER,s=e.ed25519.CURVE.Fp.create,c="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",d=K(t);if(!(0,a.equalBytes)((0,a.numberToBytesLE)(d,32),t)||(0,o.isNegativeLE)(d,i))throw new Error(c);const h=s(d*d),f=s(u+n*h),p=s(u-n*h),m=s(f*f),g=s(p*p),v=s(n*r*m-g),{isValid:y,value:E}=L(s(v*g)),b=s(E*p),w=s(E*b*v);let S=s((d+d)*b);(0,o.isNegativeLE)(S,i)&&(S=s(-S));const _=s(f*w),C=s(S*_);if(!y||(0,o.isNegativeLE)(C,i)||_===l)throw new Error(c);return new $(new e.ed25519.ExtendedPoint(S,_,u,C))}toRawBytes(){let{ex:t,ey:n,ez:r,et:i}=this.ep;const s=e.ed25519.CURVE.Fp.ORDER,c=e.ed25519.CURVE.Fp.create,d=c(c(r+n)*c(r-n)),l=c(t*n),u=c(l*l),{value:h}=L(c(d*u)),f=c(h*d),p=c(h*l),m=c(f*p*i);let g;if((0,o.isNegativeLE)(i*m,s)){let e=c(n*I),r=c(t*I);t=e,n=r,g=c(f*R)}else g=p;(0,o.isNegativeLE)(t*m,s)&&(n=c(-n));let v=c((r-n)*g);return(0,o.isNegativeLE)(v,s)&&(v=c(-v)),(0,a.numberToBytesLE)(v,32)}toHex(){return(0,a.bytesToHex)(this.toRawBytes())}toString(){return this.toHex()}equals(t){T(t);const{ex:n,ey:r}=this.ep,{ex:i,ey:o}=t.ep,s=e.ed25519.CURVE.Fp.create,a=s(n*o)===s(r*i),c=s(r*o)===s(n*i);return a||c}add(e){return T(e),new $(this.ep.add(e.ep))}subtract(e){return T(e),new $(this.ep.subtract(e.ep))}multiply(e){return new $(this.ep.multiply(e))}multiplyUnsafe(e){return new $(this.ep.multiplyUnsafe(e))}double(){return new $(this.ep.double())}negate(){return new $(this.ep.negate())}}e.RistrettoPoint=($.BASE||($.BASE=new $(e.ed25519.ExtendedPoint.BASE)),$.ZERO||($.ZERO=new $(e.ed25519.ExtendedPoint.ZERO)),$);e.hashToRistretto255=(e,r)=>{const o=r.DST,s="string"==typeof o?(0,n.utf8ToBytes)(o):o,a=(0,i.expand_message_xmd)(e,s,64,t.sha512);return $.hashToCurve(a)},e.hash_to_ristretto255=e.hashToRistretto255}(O);var Qt={},Xt={};Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.sha224=Xt.sha256=Xt.SHA256=void 0;const en=D,tn=z,nn=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),rn=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),on=new Uint32Array(64);class sn extends en.HashMD{constructor(){super(64,32,8,!1),this.A=0|rn[0],this.B=0|rn[1],this.C=0|rn[2],this.D=0|rn[3],this.E=0|rn[4],this.F=0|rn[5],this.G=0|rn[6],this.H=0|rn[7]}get(){const{A:e,B:t,C:n,D:r,E:i,F:o,G:s,H:a}=this;return[e,t,n,r,i,o,s,a]}set(e,t,n,r,i,o,s,a){this.A=0|e,this.B=0|t,this.C=0|n,this.D=0|r,this.E=0|i,this.F=0|o,this.G=0|s,this.H=0|a}process(e,t){for(let n=0;n<16;n++,t+=4)on[n]=e.getUint32(t,!1);for(let e=16;e<64;e++){const t=on[e-15],n=on[e-2],r=(0,tn.rotr)(t,7)^(0,tn.rotr)(t,18)^t>>>3,i=(0,tn.rotr)(n,17)^(0,tn.rotr)(n,19)^n>>>10;on[e]=i+on[e-7]+r+on[e-16]|0}let{A:n,B:r,C:i,D:o,E:s,F:a,G:c,H:d}=this;for(let e=0;e<64;e++){const t=d+((0,tn.rotr)(s,6)^(0,tn.rotr)(s,11)^(0,tn.rotr)(s,25))+(0,en.Chi)(s,a,c)+nn[e]+on[e]|0,l=((0,tn.rotr)(n,2)^(0,tn.rotr)(n,13)^(0,tn.rotr)(n,22))+(0,en.Maj)(n,r,i)|0;d=c,c=a,a=s,s=o+t|0,o=i,i=r,r=n,n=t+l|0}n=n+this.A|0,r=r+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,a=a+this.F|0,c=c+this.G|0,d=d+this.H|0,this.set(n,r,i,o,s,a,c,d)}roundClean(){on.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}Xt.SHA256=sn;class an extends sn{constructor(){super(),this.A=-1056596264,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=1750603025,this.G=1694076839,this.H=-1090891868,this.outputLen=28}}Xt.sha256=(0,tn.wrapConstructor)((()=>new sn)),Xt.sha224=(0,tn.wrapConstructor)((()=>new an));var cn={},dn={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hmac=e.HMAC=void 0;const t=L,n=z;class r extends n.Hash{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,(0,t.hash)(e);const i=(0,n.toBytes)(r);if(this.iHash=e.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const o=this.blockLen,s=new Uint8Array(o);s.set(i.length>o?e.create().update(i).digest():i);for(let e=0;e<s.length;e++)s[e]^=54;this.iHash.update(s),this.oHash=e.create();for(let e=0;e<s.length;e++)s[e]^=106;this.oHash.update(s),s.fill(0)}update(e){return(0,t.exists)(this),this.iHash.update(e),this}digestInto(e){(0,t.exists)(this),(0,t.bytes)(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:n,finished:r,destroyed:i,blockLen:o,outputLen:s}=this;return e.finished=r,e.destroyed=i,e.blockLen=o,e.outputLen=s,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}e.HMAC=r;e.hmac=(e,t,n)=>new r(e,t).update(n).digest(),e.hmac.create=(e,t)=>new r(e,t)}(dn);var ln={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.DER=void 0,e.weierstrassPoints=f,e.weierstrass=function(s){const a=function(e){const n=(0,t.validateBasic)(e);return r.validateObject(n,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...n})}(s),{Fp:l,n:u}=a,h=l.BYTES+1,p=2*l.BYTES+1;function m(e){return n.mod(e,u)}function g(e){return n.invert(e,u)}const{ProjectivePoint:v,normPrivateKeyToScalar:y,weierstrassEquation:E,isWithinCurveOrder:b}=f({...a,toBytes(e,t,n){const o=t.toAffine(),s=l.toBytes(o.x),a=r.concatBytes;return(0,i.abool)("isCompressed",n),n?a(Uint8Array.from([t.hasEvenY()?2:3]),s):a(Uint8Array.from([4]),s,l.toBytes(o.y))},fromBytes(e){const t=e.length,n=e[0],i=e.subarray(1);if(t!==h||2!==n&&3!==n){if(t===p&&4===n){return{x:l.fromBytes(i.subarray(0,l.BYTES)),y:l.fromBytes(i.subarray(l.BYTES,2*l.BYTES))}}throw new Error(`Point of length ${t} was invalid. Expected ${h} compressed bytes or ${p} uncompressed bytes`)}{const e=r.bytesToNumberBE(i);if(!r.inRange(e,d,l.ORDER))throw new Error("Point is not on curve");const t=E(e);let o;try{o=l.sqrt(t)}catch(e){const t=e instanceof Error?": "+e.message:"";throw new Error("Point is not on curve"+t)}return 1==(1&n)!==((o&d)===d)&&(o=l.neg(o)),{x:e,y:o}}}}),w=e=>r.bytesToHex(r.numberToBytesBE(e,a.nByteLength));function S(e){return e>u>>d}const _=(e,t,n)=>r.bytesToNumberBE(e.slice(t,n));class C{constructor(e,t,n){this.r=e,this.s=t,this.recovery=n,this.assertValidity()}static fromCompact(e){const t=a.nByteLength;return e=(0,i.ensureBytes)("compactSignature",e,2*t),new C(_(e,0,t),_(e,t,2*t))}static fromDER(t){const{r:n,s:r}=e.DER.toSig((0,i.ensureBytes)("DER",t));return new C(n,r)}assertValidity(){r.aInRange("r",this.r,d,u),r.aInRange("s",this.s,d,u)}addRecoveryBit(e){return new C(this.r,this.s,e)}recoverPublicKey(e){const{r:t,s:n,recovery:r}=this,o=A((0,i.ensureBytes)("msgHash",e));if(null==r||![0,1,2,3].includes(r))throw new Error("recovery id invalid");const s=2===r||3===r?t+a.n:t;if(s>=l.ORDER)throw new Error("recovery id 2 or 3 invalid");const c=0==(1&r)?"02":"03",d=v.fromHex(c+w(s)),u=g(s),h=m(-o*u),f=m(n*u),p=v.BASE.multiplyAndAddUnsafe(d,h,f);if(!p)throw new Error("point at infinify");return p.assertValidity(),p}hasHighS(){return S(this.s)}normalizeS(){return this.hasHighS()?new C(this.r,m(-this.s),this.recovery):this}toDERRawBytes(){return r.hexToBytes(this.toDERHex())}toDERHex(){return e.DER.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return r.hexToBytes(this.toCompactHex())}toCompactHex(){return w(this.r)+w(this.s)}}const x={isValidPrivateKey(e){try{return y(e),!0}catch(e){return!1}},normPrivateKeyToScalar:y,randomPrivateKey:()=>{const e=n.getMinHashLength(a.n);return n.mapHashToField(a.randomBytes(e),a.n)},precompute:(e=8,t=v.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};function k(e){const t=r.isBytes(e),n="string"==typeof e,i=(t||n)&&e.length;return t?i===h||i===p:n?i===2*h||i===2*p:e instanceof v}const M=a.bits2int||function(e){const t=r.bytesToNumberBE(e),n=8*e.length-a.nBitLength;return n>0?t>>BigInt(n):t},A=a.bits2int_modN||function(e){return m(M(e))},T=r.bitMask(a.nBitLength);function I(e){return r.aInRange(`num < 2^${a.nBitLength}`,e,c,T),r.numberToBytesBE(e,a.nByteLength)}function P(e,t,n=R){if(["recovered","canonical"].some((e=>e in n)))throw new Error("sign() legacy options not supported");const{hash:s,randomBytes:u}=a;let{lowS:h,prehash:f,extraEntropy:p}=n;null==h&&(h=!0),e=(0,i.ensureBytes)("msgHash",e),o(n),f&&(e=(0,i.ensureBytes)("prehashed msgHash",s(e)));const E=A(e),w=y(t),_=[I(w),I(E)];if(null!=p&&!1!==p){const e=!0===p?u(l.BYTES):p;_.push((0,i.ensureBytes)("extraEntropy",e))}const x=r.concatBytes(..._),k=E;return{seed:x,k2sig:function(e){const t=M(e);if(!b(t))return;const n=g(t),r=v.BASE.multiply(t).toAffine(),i=m(r.x);if(i===c)return;const o=m(n*m(k+i*w));if(o===c)return;let s=(r.x===i?0:2)|Number(r.y&d),a=o;return h&&S(o)&&(a=function(e){return S(e)?m(-e):e}(o),s^=1),new C(i,a,s)}}}const R={lowS:a.lowS,prehash:!1},O={lowS:a.lowS,prehash:!1};return v.BASE._setWindowSize(8),{CURVE:a,getPublicKey:function(e,t=!0){return v.fromPrivateKey(e).toRawBytes(t)},getSharedSecret:function(e,t,n=!0){if(k(e))throw new Error("first arg must be private key");if(!k(t))throw new Error("second arg must be public key");const r=v.fromHex(t);return r.multiply(y(e)).toRawBytes(n)},sign:function(e,t,n=R){const{seed:i,k2sig:o}=P(e,t,n),s=a,c=r.createHmacDrbg(s.hash.outputLen,s.nByteLength,s.hmac);return c(i,o)},verify:function(t,n,s,c=O){const d=t;if(n=(0,i.ensureBytes)("msgHash",n),s=(0,i.ensureBytes)("publicKey",s),"strict"in c)throw new Error("options.strict was renamed to lowS");o(c);const{lowS:l,prehash:u}=c;let h,f;try{if("string"==typeof d||r.isBytes(d))try{h=C.fromDER(d)}catch(t){if(!(t instanceof e.DER.Err))throw t;h=C.fromCompact(d)}else{if("object"!=typeof d||"bigint"!=typeof d.r||"bigint"!=typeof d.s)throw new Error("PARSE");{const{r:e,s:t}=d;h=new C(e,t)}}f=v.fromHex(s)}catch(e){if("PARSE"===e.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(l&&h.hasHighS())return!1;u&&(n=a.hash(n));const{r:p,s:y}=h,E=A(n),b=g(y),w=m(E*b),S=m(p*b),_=v.BASE.multiplyAndAddUnsafe(f,w,S)?.toAffine();if(!_)return!1;const x=m(_.x);return x===p},ProjectivePoint:v,Signature:C,utils:x}},e.SWUFpSqrtRatio=p,e.mapToCurveSimpleSWU=function(e,t){if(n.validateField(e),!e.isValid(t.A)||!e.isValid(t.B)||!e.isValid(t.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");const r=p(e,t.Z);if(!e.isOdd)throw new Error("Fp.isOdd is not implemented!");return n=>{let i,o,s,a,c,d,l,u;i=e.sqr(n),i=e.mul(i,t.Z),o=e.sqr(i),o=e.add(o,i),s=e.add(o,e.ONE),s=e.mul(s,t.B),a=e.cmov(t.Z,e.neg(o),!e.eql(o,e.ZERO)),a=e.mul(a,t.A),o=e.sqr(s),d=e.sqr(a),c=e.mul(d,t.A),o=e.add(o,c),o=e.mul(o,s),d=e.mul(d,a),c=e.mul(d,t.B),o=e.add(o,c),l=e.mul(i,s);const{isValid:h,value:f}=r(o,d);u=e.mul(i,n),u=e.mul(u,f),l=e.cmov(l,s,h),u=e.cmov(u,f,h);const p=e.isOdd(n)===e.isOdd(u);return u=e.cmov(e.neg(u),u,p),l=e.div(l,a),{x:l,y:u}}};const t=Ne,n=De,r=Le,i=Le;function o(e){void 0!==e.lowS&&(0,i.abool)("lowS",e.lowS),void 0!==e.prehash&&(0,i.abool)("prehash",e.prehash)}const{bytesToNumberBE:s,hexToBytes:a}=r;e.DER={Err:class extends Error{constructor(e=""){super(e)}},_tlv:{encode:(t,n)=>{const{Err:i}=e.DER;if(t<0||t>256)throw new i("tlv.encode: wrong tag");if(1&n.length)throw new i("tlv.encode: unpadded data");const o=n.length/2,s=r.numberToHexUnpadded(o);if(s.length/2&128)throw new i("tlv.encode: long form length too big");const a=o>127?r.numberToHexUnpadded(s.length/2|128):"";return`${r.numberToHexUnpadded(t)}${a}${s}${n}`},decode(t,n){const{Err:r}=e.DER;let i=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(n.length<2||n[i++]!==t)throw new r("tlv.decode: wrong tlv");const o=n[i++];let s=0;if(!!(128&o)){const e=127&o;if(!e)throw new r("tlv.decode(long): indefinite length not supported");if(e>4)throw new r("tlv.decode(long): byte length is too big");const t=n.subarray(i,i+e);if(t.length!==e)throw new r("tlv.decode: length bytes not complete");if(0===t[0])throw new r("tlv.decode(long): zero leftmost byte");for(const e of t)s=s<<8|e;if(i+=e,s<128)throw new r("tlv.decode(long): not minimal encoding")}else s=o;const a=n.subarray(i,i+s);if(a.length!==s)throw new r("tlv.decode: wrong value length");return{v:a,l:n.subarray(i+s)}}},_int:{encode(t){const{Err:n}=e.DER;if(t<c)throw new n("integer: negative integers are not allowed");let i=r.numberToHexUnpadded(t);if(8&Number.parseInt(i[0],16)&&(i="00"+i),1&i.length)throw new n("unexpected assertion");return i},decode(t){const{Err:n}=e.DER;if(128&t[0])throw new n("Invalid signature integer: negative");if(0===t[0]&&!(128&t[1]))throw new n("Invalid signature integer: unnecessary leading zero");return s(t)}},toSig(t){const{Err:n,_int:i,_tlv:o}=e.DER,s="string"==typeof t?a(t):t;r.abytes(s);const{v:c,l:d}=o.decode(48,s);if(d.length)throw new n("Invalid signature: left bytes after parsing");const{v:l,l:u}=o.decode(2,c),{v:h,l:f}=o.decode(2,u);if(f.length)throw new n("Invalid signature: left bytes after parsing");return{r:i.decode(l),s:i.decode(h)}},hexFromSig(t){const{_tlv:n,_int:r}=e.DER,i=`${n.encode(2,r.encode(t.r))}${n.encode(2,r.encode(t.s))}`;return n.encode(48,i)}};const c=BigInt(0),d=BigInt(1),l=BigInt(2),u=BigInt(3),h=BigInt(4);function f(e){const o=function(e){const n=(0,t.validateBasic)(e);r.validateObject(n,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:i,Fp:o,a:s}=n;if(i){if(!o.eql(s,o.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof i||"bigint"!=typeof i.beta||"function"!=typeof i.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...n})}(e),{Fp:s}=o,a=n.Field(o.n,o.nBitLength),l=o.toBytes||((e,t,n)=>{const i=t.toAffine();return r.concatBytes(Uint8Array.from([4]),s.toBytes(i.x),s.toBytes(i.y))}),h=o.fromBytes||(e=>{const t=e.subarray(1);return{x:s.fromBytes(t.subarray(0,s.BYTES)),y:s.fromBytes(t.subarray(s.BYTES,2*s.BYTES))}});function f(e){const{a:t,b:n}=o,r=s.sqr(e),i=s.mul(r,e);return s.add(s.add(i,s.mul(e,t)),n)}if(!s.eql(s.sqr(o.Gy),f(o.Gx)))throw new Error("bad generator point: equation left != right");function p(e){const{allowedPrivateKeyLengths:t,nByteLength:s,wrapPrivateKey:a,n:c}=o;if(t&&"bigint"!=typeof e){if(r.isBytes(e)&&(e=r.bytesToHex(e)),"string"!=typeof e||!t.includes(e.length))throw new Error("Invalid key");e=e.padStart(2*s,"0")}let l;try{l="bigint"==typeof e?e:r.bytesToNumberBE((0,i.ensureBytes)("private key",e,s))}catch(t){throw new Error(`private key must be ${s} bytes, hex or bigint, not ${typeof e}`)}return a&&(l=n.mod(l,c)),r.aInRange("private key",l,d,c),l}function m(e){if(!(e instanceof y))throw new Error("ProjectivePoint expected")}const g=(0,i.memoized)(((e,t)=>{const{px:n,py:r,pz:i}=e;if(s.eql(i,s.ONE))return{x:n,y:r};const o=e.is0();null==t&&(t=o?s.ONE:s.inv(i));const a=s.mul(n,t),c=s.mul(r,t),d=s.mul(i,t);if(o)return{x:s.ZERO,y:s.ZERO};if(!s.eql(d,s.ONE))throw new Error("invZ was invalid");return{x:a,y:c}})),v=(0,i.memoized)((e=>{if(e.is0()){if(o.allowInfinityPoint&&!s.is0(e.py))return;throw new Error("bad point: ZERO")}const{x:t,y:n}=e.toAffine();if(!s.isValid(t)||!s.isValid(n))throw new Error("bad point: x or y not FE");const r=s.sqr(n),i=f(t);if(!s.eql(r,i))throw new Error("bad point: equation left != right");if(!e.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0}));class y{constructor(e,t,n){if(this.px=e,this.py=t,this.pz=n,null==e||!s.isValid(e))throw new Error("x required");if(null==t||!s.isValid(t))throw new Error("y required");if(null==n||!s.isValid(n))throw new Error("z required");Object.freeze(this)}static fromAffine(e){const{x:t,y:n}=e||{};if(!e||!s.isValid(t)||!s.isValid(n))throw new Error("invalid affine point");if(e instanceof y)throw new Error("projective point not allowed");const r=e=>s.eql(e,s.ZERO);return r(t)&&r(n)?y.ZERO:new y(t,n,s.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(e){const t=s.invertBatch(e.map((e=>e.pz)));return e.map(((e,n)=>e.toAffine(t[n]))).map(y.fromAffine)}static fromHex(e){const t=y.fromAffine(h((0,i.ensureBytes)("pointHex",e)));return t.assertValidity(),t}static fromPrivateKey(e){return y.BASE.multiply(p(e))}static msm(e,n){return(0,t.pippenger)(y,a,e,n)}_setWindowSize(e){b.setWindowSize(this,e)}assertValidity(){v(this)}hasEvenY(){const{y:e}=this.toAffine();if(s.isOdd)return!s.isOdd(e);throw new Error("Field doesn't support isOdd")}equals(e){m(e);const{px:t,py:n,pz:r}=this,{px:i,py:o,pz:a}=e,c=s.eql(s.mul(t,a),s.mul(i,r)),d=s.eql(s.mul(n,a),s.mul(o,r));return c&&d}negate(){return new y(this.px,s.neg(this.py),this.pz)}double(){const{a:e,b:t}=o,n=s.mul(t,u),{px:r,py:i,pz:a}=this;let c=s.ZERO,d=s.ZERO,l=s.ZERO,h=s.mul(r,r),f=s.mul(i,i),p=s.mul(a,a),m=s.mul(r,i);return m=s.add(m,m),l=s.mul(r,a),l=s.add(l,l),c=s.mul(e,l),d=s.mul(n,p),d=s.add(c,d),c=s.sub(f,d),d=s.add(f,d),d=s.mul(c,d),c=s.mul(m,c),l=s.mul(n,l),p=s.mul(e,p),m=s.sub(h,p),m=s.mul(e,m),m=s.add(m,l),l=s.add(h,h),h=s.add(l,h),h=s.add(h,p),h=s.mul(h,m),d=s.add(d,h),p=s.mul(i,a),p=s.add(p,p),h=s.mul(p,m),c=s.sub(c,h),l=s.mul(p,f),l=s.add(l,l),l=s.add(l,l),new y(c,d,l)}add(e){m(e);const{px:t,py:n,pz:r}=this,{px:i,py:a,pz:c}=e;let d=s.ZERO,l=s.ZERO,h=s.ZERO;const f=o.a,p=s.mul(o.b,u);let g=s.mul(t,i),v=s.mul(n,a),E=s.mul(r,c),b=s.add(t,n),w=s.add(i,a);b=s.mul(b,w),w=s.add(g,v),b=s.sub(b,w),w=s.add(t,r);let S=s.add(i,c);return w=s.mul(w,S),S=s.add(g,E),w=s.sub(w,S),S=s.add(n,r),d=s.add(a,c),S=s.mul(S,d),d=s.add(v,E),S=s.sub(S,d),h=s.mul(f,w),d=s.mul(p,E),h=s.add(d,h),d=s.sub(v,h),h=s.add(v,h),l=s.mul(d,h),v=s.add(g,g),v=s.add(v,g),E=s.mul(f,E),w=s.mul(p,w),v=s.add(v,E),E=s.sub(g,E),E=s.mul(f,E),w=s.add(w,E),g=s.mul(v,w),l=s.add(l,g),g=s.mul(S,w),d=s.mul(b,d),d=s.sub(d,g),g=s.mul(b,v),h=s.mul(S,h),h=s.add(h,g),new y(d,l,h)}subtract(e){return this.add(e.negate())}is0(){return this.equals(y.ZERO)}wNAF(e){return b.wNAFCached(this,e,y.normalizeZ)}multiplyUnsafe(e){r.aInRange("scalar",e,c,o.n);const t=y.ZERO;if(e===c)return t;if(e===d)return this;const{endo:n}=o;if(!n)return b.unsafeLadder(this,e);let{k1neg:i,k1:a,k2neg:l,k2:u}=n.splitScalar(e),h=t,f=t,p=this;for(;a>c||u>c;)a&d&&(h=h.add(p)),u&d&&(f=f.add(p)),p=p.double(),a>>=d,u>>=d;return i&&(h=h.negate()),l&&(f=f.negate()),f=new y(s.mul(f.px,n.beta),f.py,f.pz),h.add(f)}multiply(e){const{endo:t,n:n}=o;let i,a;if(r.aInRange("scalar",e,d,n),t){const{k1neg:n,k1:r,k2neg:o,k2:c}=t.splitScalar(e);let{p:d,f:l}=this.wNAF(r),{p:u,f:h}=this.wNAF(c);d=b.constTimeNegate(n,d),u=b.constTimeNegate(o,u),u=new y(s.mul(u.px,t.beta),u.py,u.pz),i=d.add(u),a=l.add(h)}else{const{p:t,f:n}=this.wNAF(e);i=t,a=n}return y.normalizeZ([i,a])[0]}multiplyAndAddUnsafe(e,t,n){const r=y.BASE,i=(e,t)=>t!==c&&t!==d&&e.equals(r)?e.multiply(t):e.multiplyUnsafe(t),o=i(this,t).add(i(e,n));return o.is0()?void 0:o}toAffine(e){return g(this,e)}isTorsionFree(){const{h:e,isTorsionFree:t}=o;if(e===d)return!0;if(t)return t(y,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:e,clearCofactor:t}=o;return e===d?this:t?t(y,this):this.multiplyUnsafe(o.h)}toRawBytes(e=!0){return(0,i.abool)("isCompressed",e),this.assertValidity(),l(y,this,e)}toHex(e=!0){return(0,i.abool)("isCompressed",e),r.bytesToHex(this.toRawBytes(e))}}y.BASE=new y(o.Gx,o.Gy,s.ONE),y.ZERO=new y(s.ZERO,s.ONE,s.ZERO);const E=o.nBitLength,b=(0,t.wNAF)(y,o.endo?Math.ceil(E/2):E);return{CURVE:o,ProjectivePoint:y,normPrivateKeyToScalar:p,weierstrassEquation:f,isWithinCurveOrder:function(e){return r.inRange(e,d,o.n)}}}function p(e,t){const n=e.ORDER;let r=c;for(let e=n-d;e%l===c;e/=l)r+=d;const i=r,o=l<<i-d-d,s=o*l,a=(n-d)/s,f=(a-d)/l,p=s-d,m=o,g=e.pow(t,a),v=e.pow(t,(a+d)/l);let y=(t,n)=>{let r=g,o=e.pow(n,p),s=e.sqr(o);s=e.mul(s,n);let a=e.mul(t,s);a=e.pow(a,f),a=e.mul(a,o),o=e.mul(a,n),s=e.mul(a,t);let c=e.mul(s,o);a=e.pow(c,m);let u=e.eql(a,e.ONE);o=e.mul(s,v),a=e.mul(c,r),s=e.cmov(o,s,u),c=e.cmov(a,c,u);for(let t=i;t>d;t--){let n=t-l;n=l<<n-d;let i=e.pow(c,n);const a=e.eql(i,e.ONE);o=e.mul(s,r),r=e.mul(r,r),i=e.mul(c,r),s=e.cmov(o,s,a),c=e.cmov(i,c,a)}return{isValid:u,value:s}};if(e.ORDER%h===u){const n=(e.ORDER-u)/h,r=e.sqrt(e.neg(t));y=(t,i)=>{let o=e.sqr(i);const s=e.mul(t,i);o=e.mul(o,s);let a=e.pow(o,n);a=e.mul(a,s);const c=e.mul(a,r),d=e.mul(e.sqr(a),i),l=e.eql(d,t);return{isValid:l,value:e.cmov(c,a,l)}}}return y}}(ln),Object.defineProperty(cn,"__esModule",{value:!0}),cn.getHash=pn,cn.createCurve=function(e,t){const n=t=>(0,fn.weierstrass)({...e,...pn(t)});return Object.freeze({...n(t),create:n})};const un=dn,hn=z,fn=ln;function pn(e){return{hash:e,hmac:(t,...n)=>(0,un.hmac)(e,t,(0,hn.concatBytes)(...n)),randomBytes:hn.randomBytes}}!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.encodeToCurve=e.hashToCurve=e.schnorr=e.secp256k1=void 0;const t=Xt,n=z,r=cn,i=Bt,o=De,s=Le,a=ln,c=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),d=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),l=BigInt(1),u=BigInt(2),h=(e,t)=>(e+t/u)/t;function f(e){const t=c,n=BigInt(3),r=BigInt(6),i=BigInt(11),s=BigInt(22),a=BigInt(23),d=BigInt(44),l=BigInt(88),h=e*e*e%t,f=h*h*e%t,m=(0,o.pow2)(f,n,t)*f%t,g=(0,o.pow2)(m,n,t)*f%t,v=(0,o.pow2)(g,u,t)*h%t,y=(0,o.pow2)(v,i,t)*v%t,E=(0,o.pow2)(y,s,t)*y%t,b=(0,o.pow2)(E,d,t)*E%t,w=(0,o.pow2)(b,l,t)*b%t,S=(0,o.pow2)(w,d,t)*E%t,_=(0,o.pow2)(S,n,t)*f%t,C=(0,o.pow2)(_,a,t)*y%t,x=(0,o.pow2)(C,r,t)*h%t,k=(0,o.pow2)(x,u,t);if(!p.eql(p.sqr(k),e))throw new Error("Cannot find square root");return k}const p=(0,o.Field)(c,void 0,void 0,{sqrt:f});e.secp256k1=(0,r.createCurve)({a:BigInt(0),b:BigInt(7),Fp:p,n:d,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:e=>{const t=d,n=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-l*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=n,a=BigInt("0x100000000000000000000000000000000"),c=h(s*e,t),u=h(-r*e,t);let f=(0,o.mod)(e-c*n-u*i,t),p=(0,o.mod)(-c*r-u*s,t);const m=f>a,g=p>a;if(m&&(f=t-f),g&&(p=t-p),f>a||p>a)throw new Error("splitScalar: Endomorphism failed, k="+e);return{k1neg:m,k1:f,k2neg:g,k2:p}}}},t.sha256);const m=BigInt(0),g={};function v(e,...n){let r=g[e];if(void 0===r){const n=(0,t.sha256)(Uint8Array.from(e,(e=>e.charCodeAt(0))));r=(0,s.concatBytes)(n,n),g[e]=r}return(0,t.sha256)((0,s.concatBytes)(r,...n))}const y=e=>e.toRawBytes(!0).slice(1),E=e=>(0,s.numberToBytesBE)(e,32),b=e=>(0,o.mod)(e,c),w=e=>(0,o.mod)(e,d),S=e.secp256k1.ProjectivePoint,_=(e,t,n)=>S.BASE.multiplyAndAddUnsafe(e,t,n);function C(t){let n=e.secp256k1.utils.normPrivateKeyToScalar(t),r=S.fromPrivateKey(n);return{scalar:r.hasEvenY()?n:w(-n),bytes:y(r)}}function x(e){(0,s.aInRange)("x",e,l,c);const t=b(e*e);let n=f(b(t*e+BigInt(7)));n%u!==m&&(n=b(-n));const r=new S(e,n,l);return r.assertValidity(),r}const k=s.bytesToNumberBE;function M(...e){return w(k(v("BIP0340/challenge",...e)))}function A(e){return C(e).bytes}function T(e,t,r=(0,n.randomBytes)(32)){const i=(0,s.ensureBytes)("message",e),{bytes:o,scalar:a}=C(t),c=(0,s.ensureBytes)("auxRand",r,32),d=E(a^k(v("BIP0340/aux",c))),l=v("BIP0340/nonce",d,o,i),u=w(k(l));if(u===m)throw new Error("sign failed: k is zero");const{bytes:h,scalar:f}=C(u),p=M(h,o,i),g=new Uint8Array(64);if(g.set(h,0),g.set(E(w(f+p*a)),32),!I(g,i,o))throw new Error("sign: Invalid signature produced");return g}function I(e,t,n){const r=(0,s.ensureBytes)("signature",e,64),i=(0,s.ensureBytes)("message",t),o=(0,s.ensureBytes)("publicKey",n,32);try{const e=x(k(o)),t=k(r.subarray(0,32));if(!(0,s.inRange)(t,l,c))return!1;const n=k(r.subarray(32,64));if(!(0,s.inRange)(n,l,d))return!1;const a=M(E(t),y(e),i),u=_(e,n,w(-a));return!(!u||!u.hasEvenY()||u.toAffine().x!==t)}catch(e){return!1}}e.schnorr={getPublicKey:A,sign:T,verify:I,utils:{randomPrivateKey:e.secp256k1.utils.randomPrivateKey,lift_x:x,pointToBytes:y,numberToBytesBE:s.numberToBytesBE,bytesToNumberBE:s.bytesToNumberBE,taggedHash:v,mod:o.mod}};const P=(()=>(0,i.isogenyMap)(p,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map((e=>e.map((e=>BigInt(e)))))))(),R=(()=>(0,a.mapToCurveSimpleSWU)(p,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:p.create(BigInt("-11"))}))(),O=(()=>(0,i.createHasher)(e.secp256k1.ProjectivePoint,(e=>{const{x:t,y:n}=R(p.create(e[0]));return P(t,n)}),{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:p.ORDER,m:1,k:128,expand:"xmd",hash:t.sha256}))();e.hashToCurve=O.hashToCurve,e.encodeToCurve=O.encodeToCurve}(Qt);var mn={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.decodeHex=e.remove0x=void 0;var t=p;e.remove0x=function(e){return e.startsWith("0x")||e.startsWith("0X")?e.slice(2):e};e.decodeHex=function(n){return(0,t.hexToBytes)((0,e.remove0x)(n))}}(mn),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.hexToPublicKey=e.convertPublicKeyFormat=e.getSharedPoint=e.getPublicKey=e.isValidPrivateKey=e.getValidSecret=void 0;var t=I,n=O,r=Qt,i=C,o=x,s=mn;e.getValidSecret=function(){var n;do{n=(0,t.randomBytes)(o.SECRET_KEY_LENGTH)}while(!(0,e.isValidPrivateKey)(n));return n};e.isValidPrivateKey=function(e){return a((0,i.ellipticCurve)(),(function(t){return t.utils.isValidPrivateKey(e)}),(function(){return!0}),(function(){return!0}))};e.getPublicKey=function(e){return a((0,i.ellipticCurve)(),(function(t){return t.getPublicKey(e)}),(function(t){return t.getPublicKey(e)}),(function(t){return t.getPublicKey(e)}))};e.getSharedPoint=function(e,t,n){return a((0,i.ellipticCurve)(),(function(r){return r.getSharedSecret(e,t,n)}),(function(n){return n.getSharedSecret(e,t)}),(function(n){return d(n,e,t)}))};e.convertPublicKeyFormat=function(e,t){return a((0,i.ellipticCurve)(),(function(n){return n.getSharedSecret(BigInt(1),e,t)}),(function(){return e}),(function(){return e}))};function a(e,t,i,o){if("secp256k1"===e)return t(r.secp256k1);if("x25519"===e)return i(n.x25519);if("ed25519"===e)return o(n.ed25519);throw new Error("Not implemented")}e.hexToPublicKey=function(e){var t=(0,s.decodeHex)(e);return a((0,i.ellipticCurve)(),(function(){return c(t)}),(function(){return t}),(function(){return t}))};var c=function(e){if(e.length===o.ETH_PUBLIC_KEY_SIZE){var t=new Uint8Array(1+e.length);return t.set([4]),t.set(e,1),t}return e},d=function(e,t,n){var r=e.utils.getExtendedPublicKey(t).scalar;return e.ExtendedPoint.fromHex(n).multiply(r).toRawBytes()}}(T);var gn={},vn={};Object.defineProperty(vn,"__esModule",{value:!0}),vn.hkdf=void 0,vn.extract=wn,vn.expand=Cn;const yn=L,En=z,bn=dn;function wn(e,t,n){return(0,yn.hash)(e),void 0===n&&(n=new Uint8Array(e.outputLen)),(0,bn.hmac)(e,(0,En.toBytes)(n),(0,En.toBytes)(t))}const Sn=new Uint8Array([0]),_n=new Uint8Array;function Cn(e,t,n,r=32){if((0,yn.hash)(e),(0,yn.number)(r),r>255*e.outputLen)throw new Error("Length should be <= 255*HashLen");const i=Math.ceil(r/e.outputLen);void 0===n&&(n=_n);const o=new Uint8Array(i*e.outputLen),s=bn.hmac.create(e,t),a=s._cloneInto(),c=new Uint8Array(s.outputLen);for(let t=0;t<i;t++)Sn[0]=t+1,a.update(0===t?_n:c).update(n).update(Sn).digestInto(c),o.set(c,e.outputLen*t),s._cloneInto(a);return s.destroy(),a.destroy(),c.fill(0),Sn.fill(0),o.slice(0,r)}vn.hkdf=(e,t,n,r,i)=>Cn(e,wn(e,t,n),r,i),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getSharedKey=e.deriveKey=void 0;var t=p,n=vn,r=Xt;e.deriveKey=function(e,t,i){return(0,n.hkdf)(r.sha256,e,t,i,32)};e.getSharedKey=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return(0,e.deriveKey)(t.concatBytes.apply(void 0,n))}}(gn);var xn={},kn={},Mn={};Object.defineProperty(Mn,"__esModule",{value:!0}),Mn._compat=void 0;var An=p,Tn=r;Mn._compat=function(e,t,n,r){var i="aes-256-gcm"===e||"chacha20-poly1305"===e,o=i?16:0,s=i?{authTagLength:o}:void 0;return{encrypt:function(o){var a=(0,Tn.createCipheriv)(e,t,n,s);i&&void 0!==r&&a.setAAD(r);var c=a.update(o),d=a.final();return i?(0,An.concatBytes)(c,d,a.getAuthTag()):(0,An.concatBytes)(c,d)},decrypt:function(a){var c=a.subarray(0,a.length-o),d=a.subarray(a.length-o),l=(0,Tn.createDecipheriv)(e,t,n,s);i&&(void 0!==r&&l.setAAD(r),l.setAuthTag(d));var u=l.update(c),h=l.final();return(0,An.concatBytes)(u,h)}}},Object.defineProperty(kn,"__esModule",{value:!0}),kn.aes256cbc=kn.aes256gcm=void 0;var In=Mn;kn.aes256gcm=function(e,t,n){return(0,In._compat)("aes-256-gcm",e,t,n)};kn.aes256cbc=function(e,t,n){return(0,In._compat)("aes-256-cbc",e,t)};var Pn={},Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Rn._hchacha=void 0;Rn._hchacha=function(e,t,n,r){for(var i=e[0],o=e[1],s=e[2],a=e[3],c=t[0],d=t[1],l=t[2],u=t[3],h=t[4],f=t[5],p=t[6],m=t[7],g=n[0],v=n[1],y=n[2],E=n[3],b=0;b<20;b+=2)g=On(g^(i=i+c|0),16),c=On(c^(h=h+g|0),12),g=On(g^(i=i+c|0),8),c=On(c^(h=h+g|0),7),v=On(v^(o=o+d|0),16),d=On(d^(f=f+v|0),12),v=On(v^(o=o+d|0),8),d=On(d^(f=f+v|0),7),y=On(y^(s=s+l|0),16),l=On(l^(p=p+y|0),12),y=On(y^(s=s+l|0),8),l=On(l^(p=p+y|0),7),E=On(E^(a=a+u|0),16),u=On(u^(m=m+E|0),12),E=On(E^(a=a+u|0),8),u=On(u^(m=m+E|0),7),E=On(E^(i=i+d|0),16),d=On(d^(p=p+E|0),12),E=On(E^(i=i+d|0),8),d=On(d^(p=p+E|0),7),g=On(g^(o=o+l|0),16),l=On(l^(m=m+g|0),12),g=On(g^(o=o+l|0),8),l=On(l^(m=m+g|0),7),v=On(v^(s=s+u|0),16),u=On(u^(h=h+v|0),12),v=On(v^(s=s+u|0),8),u=On(u^(h=h+v|0),7),y=On(y^(a=a+c|0),16),c=On(c^(f=f+y|0),12),y=On(y^(a=a+c|0),8),c=On(c^(f=f+y|0),7);var w=0;r[w++]=i,r[w++]=o,r[w++]=s,r[w++]=a,r[w++]=g,r[w++]=v,r[w++]=y,r[w++]=E};var On=function(e,t){return e<<t|e>>>32-t};Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.xchacha20=void 0;var Nn=p,Dn=Mn,Ln=Rn;Pn.xchacha20=function(e,t,n){if(24!==t.length)throw new Error("xchacha20's nonce must be 24 bytes");var r=new Uint32Array([1634760805,857760878,2036477234,1797285236]),i=new Uint32Array(8);(0,Ln._hchacha)(r,(0,Nn.u32)(e),(0,Nn.u32)(t.subarray(0,16)),i);var o=new Uint8Array(12);return o.set([0,0,0,0]),o.set(t.subarray(16),4),(0,Dn._compat)("chacha20-poly1305",(0,Nn.u8)(i),o,n)},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.aesDecrypt=e.aesEncrypt=e.symDecrypt=e.symEncrypt=void 0;var t=p,n=I,r=kn,i=Pn,o=C,s=x;e.symEncrypt=function(e,t,n){return a(c,e,t,n)};function a(e,t,n,a){var c=(0,o.symmetricAlgorithm)();if("aes-256-gcm"===c)return e(r.aes256gcm,t,n,(0,o.symmetricNonceLength)(),s.AEAD_TAG_LENGTH,a);if("xchacha20"===c)return e(i.xchacha20,t,n,s.XCHACHA20_NONCE_LENGTH,s.AEAD_TAG_LENGTH,a);if("aes-256-cbc"===c)return e(r.aes256cbc,t,n,16,0);throw new Error("Not implemented")}function c(e,r,i,o,s,a){var c=(0,n.randomBytes)(o),d=e(r,c,a).encrypt(i);if(0===s)return(0,t.concatBytes)(c,d);var l=d.length-s,u=d.subarray(0,l),h=d.subarray(l);return(0,t.concatBytes)(c,h,u)}function d(e,n,r,i,o,s){var a=r.subarray(0,i),c=e(n,Uint8Array.from(a),s),d=r.subarray(i);if(0===o)return c.decrypt(d);var l=d.subarray(0,o),u=d.subarray(o);return c.decrypt((0,t.concatBytes)(u,l))}e.symDecrypt=function(e,t,n){return a(d,e,t,n)},e.aesEncrypt=e.symEncrypt,e.aesDecrypt=e.symDecrypt}(xn),function(e){var t=u&&u.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),n=u&&u.__exportStar||function(e,n){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(n,r)||t(n,e,r)};Object.defineProperty(e,"__esModule",{value:!0}),n(T,e),n(gn,e),n(mn,e),n(xn,e)}(A);var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.PublicKey=void 0;var Kn=p,jn=A,$n=function(){function e(e){this.data=(0,jn.convertPublicKeyFormat)(e,!0)}return e.fromHex=function(t){return new e((0,jn.hexToPublicKey)(t))},Object.defineProperty(e.prototype,"uncompressed",{get:function(){return Buffer.from((0,jn.convertPublicKeyFormat)(this.data,!1))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"compressed",{get:function(){return Buffer.from(this.data)},enumerable:!1,configurable:!0}),e.prototype.toHex=function(e){return void 0===e&&(e=!0),(0,Kn.bytesToHex)(e?this.data:this.uncompressed)},e.prototype.decapsulate=function(e,t){void 0===t&&(t=!1);var n=t?this.data:this.uncompressed,r=e.multiply(this,t);return(0,jn.getSharedKey)(n,r)},e.prototype.equals=function(e){return(0,Kn.equalBytes)(this.data,e.data)},e}();Bn.PublicKey=$n,Object.defineProperty(M,"__esModule",{value:!0}),M.PrivateKey=void 0;var Hn=p,Un=A,Fn=Bn,qn=function(){function e(e){if(void 0===e)this.data=(0,Un.getValidSecret)();else{if(!(0,Un.isValidPrivateKey)(e))throw new Error("Invalid private key");this.data=e}this.publicKey=new Fn.PublicKey((0,Un.getPublicKey)(this.data))}return e.fromHex=function(t){return new e((0,Un.decodeHex)(t))},Object.defineProperty(e.prototype,"secret",{get:function(){return Buffer.from(this.data)},enumerable:!1,configurable:!0}),e.prototype.toHex=function(){return(0,Hn.bytesToHex)(this.data)},e.prototype.encapsulate=function(e,t){void 0===t&&(t=!1);var n=t?this.publicKey.compressed:this.publicKey.uncompressed,r=this.multiply(e,t);return(0,Un.getSharedKey)(n,r)},e.prototype.multiply=function(e,t){return void 0===t&&(t=!1),(0,Un.getSharedPoint)(this.data,e.compressed,t)},e.prototype.equals=function(e){return(0,Hn.equalBytes)(this.data,e.data)},e}();M.PrivateKey=qn,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.PublicKey=e.PrivateKey=void 0;var t=M;Object.defineProperty(e,"PrivateKey",{enumerable:!0,get:function(){return t.PrivateKey}});var n=Bn;Object.defineProperty(e,"PublicKey",{enumerable:!0,get:function(){return n.PublicKey}})}(k),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.utils=e.PublicKey=e.PrivateKey=e.ECIES_CONFIG=void 0,e.encrypt=function(e,o){var s=new r.PrivateKey,a=e instanceof Uint8Array?new r.PublicKey(e):r.PublicKey.fromHex(e),c=s.encapsulate(a,(0,n.isHkdfKeyCompressed)()),d=(0,n.isEphemeralKeyCompressed)()?s.publicKey.compressed:s.publicKey.uncompressed,l=(0,i.symEncrypt)(c,o);return Buffer.from((0,t.concatBytes)(d,l))},e.decrypt=function(e,t){var o=e instanceof Uint8Array?new r.PrivateKey(e):r.PrivateKey.fromHex(e),s=(0,n.ephemeralKeySize)(),a=new r.PublicKey(t.subarray(0,s)),c=t.subarray(s),d=a.decapsulate(o,(0,n.isHkdfKeyCompressed)());return Buffer.from((0,i.symDecrypt)(d,c))};var t=p,n=C,r=k,i=A;var o=C;Object.defineProperty(e,"ECIES_CONFIG",{enumerable:!0,get:function(){return o.ECIES_CONFIG}});var s=k;Object.defineProperty(e,"PrivateKey",{enumerable:!0,get:function(){return s.PrivateKey}}),Object.defineProperty(e,"PublicKey",{enumerable:!0,get:function(){return s.PublicKey}}),e.utils={aesEncrypt:i.aesEncrypt,aesDecrypt:i.aesDecrypt,symEncrypt:i.symEncrypt,symDecrypt:i.symDecrypt,decodeHex:i.decodeHex,getValidSecret:i.getValidSecret,remove0x:i.remove0x}}(f);const zn=t("KeyExchange:Layer"),Wn=t("SocketService:Layer"),Vn=t("Ecies:Layer"),Gn=t("RemoteCommunication:Layer");zn.color="##95c44e",Wn.color="#f638d7",Vn.color="#465b9c",Gn.color="#47a2be";const Yn={KeyExchange:zn,SocketService:Wn,Ecies:Vn,RemoteCommunication:Gn};let Zn,Jn=[],Qn=[];const Xn=(t,n)=>l(void 0,void 0,void 0,(function*(){Zn=n,Qn.push(t),function(t){return l(this,void 0,void 0,(function*(){if(!Zn||!t)return;!function(){const e=Qn;Qn=Jn,Jn=e}();const n=Zn.endsWith("/")?`${Zn}evt`:`${Zn}/evt`,r=Object.assign({},t);if(delete r.params,t.params)for(const[e,n]of Object.entries(t.params))r[e]=n;const i=JSON.stringify(r);Yn.RemoteCommunication(`[sendBufferedEvents] Sending ${Jn.length} analytics events to ${n}`);try{const t=yield e(n,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:i}),r=yield t.text();Yn.RemoteCommunication(`[sendBufferedEvents] Response: ${r}`),Jn.length=0}catch(n){}}))}(t).catch((()=>{}))}));class er{constructor(e){this.enabled=!0,(null==e?void 0:e.debug)&&t.enable("Ecies:Layer"),this.ecies=(null==e?void 0:e.privateKey)?f.PrivateKey.fromHex(e.privateKey):new f.PrivateKey,Yn.Ecies("[ECIES constructor()] initialized secret: ",this.ecies.toHex()),Yn.Ecies("[ECIES constructor()] initialized public: ",this.ecies.publicKey.toHex()),Yn.Ecies("[ECIES constructor()] init with",this)}generateECIES(){this.ecies=new f.PrivateKey}getPublicKey(){return this.ecies.publicKey.toHex()}encrypt(e,t){let r=e;if(this.enabled)try{Yn.Ecies("[ECIES: encrypt()] using otherPublicKey",t);const i=n.Buffer.from(e),o=f.encrypt(t,i);r=n.Buffer.from(o).toString("base64")}catch(r){throw Yn.Ecies("[ECIES: encrypt()] error encrypt:",r),Yn.Ecies("[ECIES: encrypt()] private: ",this.ecies.toHex()),Yn.Ecies("[ECIES: encrypt()] data: ",e),Yn.Ecies("[ECIES: encrypt()] otherkey: ",t),r}return r}decrypt(e){let t=e;if(this.enabled)try{Yn.Ecies("[ECIES: decrypt()] using privateKey",this.ecies.toHex());const r=n.Buffer.from(e.toString(),"base64");t=f.decrypt(this.ecies.toHex(),r).toString()}catch(t){throw Yn.Ecies("[ECIES: decrypt()] error decrypt",t),Yn.Ecies("[ECIES: decrypt()] private: ",this.ecies.toHex()),Yn.Ecies("[ECIES: decrypt()] encryptedData: ",e),t}return t}getKeyInfo(){return{private:this.ecies.toHex(),public:this.ecies.publicKey.toHex()}}toString(){Yn.Ecies("[ECIES: toString()]",this.getKeyInfo())}}var tr={name:"@metamask/sdk-communication-layer",version:"0.32.0",description:"",homepage:"https://github.com/MetaMask/metamask-sdk#readme",bugs:{url:"https://github.com/MetaMask/metamask-sdk/issues"},repository:{type:"git",url:"https://github.com/MetaMask/metamask-sdk.git",directory:"packages/sdk-communication-layer"},main:"dist/node/cjs/metamask-sdk-communication-layer.js",unpkg:"dist/browser/umd/metamask-sdk-communication-layer.js",module:"dist/node/es/metamask-sdk-communication-layer.js",browser:"dist/browser/es/metamask-sdk-communication-layer.js","react-native":"dist/react-native/es/metamask-sdk-communication-layer.js",types:"dist/types/src/index.d.ts",files:["/dist"],scripts:{"build:types":"tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types","build:clean":"yarn clean && yarn build",build:"yarn build:types && rollup -c --bundleConfigAsCjs","build:dev":"yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs",dev:'concurrently "tsc --watch" "rollup -c --bundleConfigAsCjs -w"',"build:post-tsc":"echo 'N/A'","build:pre-tsc":"echo 'N/A'",size:"size-limit",clean:"rimraf ./dist",lint:"yarn lint:eslint && yarn lint:misc --check","lint:changelog":"../../scripts/validate-changelog.sh @metamask/sdk-communication-layer","lint:eslint":"eslint . --cache --ext js,ts","lint:fix":"yarn lint:eslint --fix && yarn lint:misc --write","lint:misc":"prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore","publish:preview":"yarn npm publish --tag preview",prepack:"../../scripts/prepack.sh",reset:"yarn clean && rimraf ./node_modules/",test:'jest --testPathIgnorePatterns "/e2e/"',"test:e2e":'jest --testPathPattern "/e2e/"',"test:coverage":"jest --coverage","test:ci":'jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns "/e2e/"',"test:dev":"jest",watch:"rollup -c --bundleConfigAsCjs -w"},dependencies:{bufferutil:"^4.0.8","date-fns":"^2.29.3",debug:"^4.3.4","utf-8-validate":"^5.0.2",uuid:"^8.3.2"},devDependencies:{"@jest/globals":"^29.3.1","@lavamoat/allow-scripts":"^2.3.1","@metamask/auto-changelog":"3.1.0","@metamask/eslint-config":"^6.0.0","@metamask/eslint-config-nodejs":"^6.0.0","@metamask/eslint-config-typescript":"^6.0.0","@rollup/plugin-commonjs":"^25.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-replace":"^6.0.1","@rollup/plugin-terser":"^0.4.4","@size-limit/preset-big-lib":"^11.0.2","@types/jest":"^29.2.4","@types/node":"^20.1.3","@types/uuid":"^9.0.0","@typescript-eslint/eslint-plugin":"^4.26.0","@typescript-eslint/parser":"^4.26.0",concurrently:"^9.1.2","cross-fetch":"^4.0.0",eciesjs:"^0.4.11",eslint:"^7.30.0","eslint-config-prettier":"^8.3.0","eslint-plugin-import":"^2.23.4","eslint-plugin-jest":"^24.4.0","eslint-plugin-jsdoc":"^36.1.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^3.4.0",eventemitter2:"^6.4.9",jest:"^29.3.1",prettier:"^2.3.0",rimraf:"^3.0.2",rollup:"^4.26.0","rollup-plugin-jscc":"^2.0.0","rollup-plugin-natives":"^0.7.5","rollup-plugin-node-builtins":"^2.1.2","rollup-plugin-node-globals":"^1.4.0","rollup-plugin-peer-deps-external":"^2.2.4","rollup-plugin-polyfill-node":"^0.13.0","rollup-plugin-sizes":"^1.0.6","rollup-plugin-typescript2":"^0.31.2","rollup-plugin-visualizer":"^5.12.0","size-limit":"^11.1.6","socket.io-client":"^4.5.1","stream-browserify":"^3.0.0","ts-jest":"^29.0.3","ts-node":"^10.9.1",typescript:"^5.6.3"},peerDependencies:{"cross-fetch":"^4.0.0",eciesjs:"*",eventemitter2:"^6.4.9","readable-stream":"^3.6.2","socket.io-client":"^4.5.1"},publishConfig:{access:"public",registry:"https://registry.npmjs.org/"},lavamoat:{allowScripts:{"@lavamoat/preinstall-always-fail":!1,canvas:!0,"eciesjs>secp256k1":!1,"socket.io-client>engine.io-client>ws>bufferutil":!1,"socket.io-client>engine.io-client>ws>utf-8-validate":!1,bufferutil:!1,"utf-8-validate":!1}}};const nr="https://metamask-sdk.api.cx.metamask.io/",rr=["websocket"],ir=6048e5,or=3e3,sr={METAMASK_GETPROVIDERSTATE:"metamask_getProviderState",ETH_REQUESTACCOUNTS:"eth_requestAccounts"};function ar(e){const{context:t}=e;Yn.RemoteCommunication(`[RemoteCommunication: clean()] context=${t}`),e.channelConfig=void 0,e.ready=!1,e.originatorConnectStarted=!1}var cr,dr,lr,ur;exports.ConnectionStatus=void 0,exports.EventType=void 0,exports.MessageType=void 0,(ur=exports.ConnectionStatus||(exports.ConnectionStatus={})).DISCONNECTED="disconnected",ur.WAITING="waiting",ur.TIMEOUT="timeout",ur.LINKED="linked",ur.PAUSED="paused",ur.TERMINATED="terminated",function(e){e.KEY_INFO="key_info",e.SERVICE_STATUS="service_status",e.PROVIDER_UPDATE="provider_update",e.RPC_UPDATE="rpc_update",e.KEYS_EXCHANGED="keys_exchanged",e.JOIN_CHANNEL="join_channel",e.PUBLIC_KEY="public_key",e.CHANNEL_CREATED="channel_created",e.CLIENTS_CONNECTED="clients_connected",e.CLIENTS_DISCONNECTED="clients_disconnected",e.CLIENTS_WAITING="clients_waiting",e.CLIENTS_READY="clients_ready",e.REJECTED="rejected",e.WALLET_INIT="wallet_init",e.CHANNEL_PERSISTENCE="channel_persistence",e.CONFIG="config",e.MESSAGE_ACK="ack",e.SOCKET_DISCONNECTED="socket_disconnected",e.SOCKET_RECONNECT="socket_reconnect",e.OTP="otp",e.SDK_RPC_CALL="sdk_rpc_call",e.AUTHORIZED="authorized",e.CONNECTION_STATUS="connection_status",e.MESSAGE="message",e.TERMINATE="terminate"}(exports.EventType||(exports.EventType={})),(cr||(cr={})).KEY_EXCHANGE="key_exchange",function(e){e.KEY_HANDSHAKE_START="key_handshake_start",e.KEY_HANDSHAKE_CHECK="key_handshake_check",e.KEY_HANDSHAKE_SYN="key_handshake_SYN",e.KEY_HANDSHAKE_SYNACK="key_handshake_SYNACK",e.KEY_HANDSHAKE_ACK="key_handshake_ACK",e.KEY_HANDSHAKE_WALLET="key_handshake_wallet",e.KEY_HANDSHAKE_NONE="none"}(dr||(dr={}));class hr extends i.EventEmitter2{constructor({communicationLayer:e,otherPublicKey:t,context:n,ecies:r,logging:i}){super(),this.keysExchanged=!1,this.step=dr.KEY_HANDSHAKE_NONE,this.debug=!1,this.context=n,this.communicationLayer=e,(null==r?void 0:r.privateKey)&&t&&(Yn.KeyExchange(`[KeyExchange: constructor()] otherPubKey=${t} set keysExchanged to true!`,r),this.keysExchanged=!0),this.myECIES=new er(Object.assign(Object.assign({},r),{debug:null==i?void 0:i.eciesLayer})),this.communicationLayer.state.eciesInstance=this.myECIES,this.myPublicKey=this.myECIES.getPublicKey(),this.debug=!0===(null==i?void 0:i.keyExchangeLayer),t&&this.setOtherPublicKey(t),this.communicationLayer.on(cr.KEY_EXCHANGE,this.onKeyExchangeMessage.bind(this))}onKeyExchangeMessage(e){const{relayPersistence:t}=this.communicationLayer.remote.state;if(Yn.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} keysExchanged=${this.keysExchanged} relayPersistence=${t}`,e),t)return void Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Ignoring key exchange message because relay persistence is activated");const{message:n}=e;this.keysExchanged&&Yn.KeyExchange(`[KeyExchange: onKeyExchangeMessage()] context=${this.context} received handshake while already exchanged. step=${this.step} otherPubKey=${this.otherPublicKey}`),this.emit(exports.EventType.KEY_INFO,n.type),n.type===dr.KEY_HANDSHAKE_SYN?(this.checkStep([dr.KEY_HANDSHAKE_NONE,dr.KEY_HANDSHAKE_ACK]),Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYN",n),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:dr.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey}).catch((e=>{Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_SYNACK",e)})),this.setStep(dr.KEY_HANDSHAKE_ACK)):n.type===dr.KEY_HANDSHAKE_SYNACK?(this.checkStep([dr.KEY_HANDSHAKE_SYNACK,dr.KEY_HANDSHAKE_ACK,dr.KEY_HANDSHAKE_NONE]),Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_SYNACK"),n.pubkey&&this.setOtherPublicKey(n.pubkey),this.communicationLayer.sendMessage({type:dr.KEY_HANDSHAKE_ACK}).catch((e=>{Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] Error sending KEY_HANDSHAKE_ACK",e)})),this.keysExchanged=!0,this.setStep(dr.KEY_HANDSHAKE_ACK),this.emit(exports.EventType.KEYS_EXCHANGED)):n.type===dr.KEY_HANDSHAKE_ACK&&(Yn.KeyExchange("[KeyExchange: onKeyExchangeMessage()] KEY_HANDSHAKE_ACK set keysExchanged to true!"),this.checkStep([dr.KEY_HANDSHAKE_ACK,dr.KEY_HANDSHAKE_NONE]),this.keysExchanged=!0,this.setStep(dr.KEY_HANDSHAKE_ACK),this.emit(exports.EventType.KEYS_EXCHANGED))}resetKeys(e){this.clean(),this.myECIES=new er(e)}clean(){Yn.KeyExchange(`[KeyExchange: clean()] context=${this.context} reset handshake state`),this.setStep(dr.KEY_HANDSHAKE_NONE),this.emit(exports.EventType.KEY_INFO,this.step),this.keysExchanged=!1}start({isOriginator:e,force:t}){const{relayPersistence:n,protocolVersion:r}=this.communicationLayer.remote.state,i=r>=2;n?Yn.KeyExchange("[KeyExchange: start()] Ignoring key exchange message because relay persistence is activated"):(Yn.KeyExchange(`[KeyExchange: start()] context=${this.context} protocolVersion=${r} isOriginator=${e} step=${this.step} force=${t} relayPersistence=${n} keysExchanged=${this.keysExchanged}`),e?!(this.keysExchanged||this.step!==dr.KEY_HANDSHAKE_NONE&&this.step!==dr.KEY_HANDSHAKE_SYNACK)||t?(Yn.KeyExchange(`[KeyExchange: start()] context=${this.context} -- start key exchange (force=${t}) -- step=${this.step}`,this.step),this.clean(),this.setStep(dr.KEY_HANDSHAKE_SYNACK),this.communicationLayer.sendMessage({type:dr.KEY_HANDSHAKE_SYN,pubkey:this.myPublicKey,v:2}).catch((e=>{Yn.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYN",e)}))):Yn.KeyExchange(`[KeyExchange: start()] context=${this.context} -- key exchange already ${this.keysExchanged?"done":"in progress"} -- aborted.`,this.step):this.keysExchanged&&!0!==t?Yn.KeyExchange("[KeyExchange: start()] don't send KEY_HANDSHAKE_START -- exchange already done."):i?this.communicationLayer.sendMessage({type:dr.KEY_HANDSHAKE_SYNACK,pubkey:this.myPublicKey,v:2}).catch((e=>{Yn.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_SYNACK",e)})):(this.communicationLayer.sendMessage({type:dr.KEY_HANDSHAKE_START}).catch((e=>{Yn.KeyExchange("[KeyExchange: start()] Error sending KEY_HANDSHAKE_START",e)})),this.clean()))}setStep(e){this.step=e,this.emit(exports.EventType.KEY_INFO,e)}checkStep(e){e.length>0&&e.indexOf(this.step.toString())}setRelayPersistence({localKey:e,otherKey:t}){this.otherPublicKey=t,this.myECIES=new er({privateKey:e,debug:this.debug}),this.keysExchanged=!0}setKeysExchanged(e){this.keysExchanged=e}areKeysExchanged(){return this.keysExchanged}getMyPublicKey(){return this.myPublicKey}getOtherPublicKey(){return this.otherPublicKey}setOtherPublicKey(e){Yn.KeyExchange("[KeyExchange: setOtherPubKey()]",e),this.otherPublicKey=e}encryptMessage(e){if(!this.otherPublicKey)throw new Error("encryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.encrypt(e,this.otherPublicKey)}decryptMessage(e){if(!this.otherPublicKey)throw new Error("decryptMessage: Keys not exchanged - missing otherPubKey");return this.myECIES.decrypt(e)}getKeyInfo(){return{ecies:Object.assign(Object.assign({},this.myECIES.getKeyInfo()),{otherPubKey:this.otherPublicKey}),step:this.step,keysExchanged:this.areKeysExchanged()}}toString(){const e={keyInfo:this.getKeyInfo(),keysExchanged:this.keysExchanged,step:this.step};return JSON.stringify(e)}}!function(e){e.TERMINATE="terminate",e.ANSWER="answer",e.OFFER="offer",e.CANDIDATE="candidate",e.JSONRPC="jsonrpc",e.WALLET_INFO="wallet_info",e.WALLET_INIT="wallet_init",e.ORIGINATOR_INFO="originator_info",e.PAUSE="pause",e.OTP="otp",e.AUTHORIZED="authorized",e.PING="ping",e.READY="ready"}(exports.MessageType||(exports.MessageType={})),function(e){e.REQUEST="sdk_connect_request_started",e.REQUEST_MOBILE="sdk_connect_request_started_mobile",e.RECONNECT="sdk_reconnect_request_started",e.CONNECTED="sdk_connection_established",e.CONNECTED_MOBILE="sdk_connection_established_mobile",e.AUTHORIZED="sdk_connection_authorized",e.REJECTED="sdk_connection_rejected",e.TERMINATED="sdk_connection_terminated",e.DISCONNECTED="sdk_disconnected",e.SDK_USE_EXTENSION="sdk_use_extension",e.SDK_RPC_REQUEST="sdk_rpc_request",e.SDK_RPC_REQUEST_RECEIVED="sdk_rpc_request_received",e.SDK_RPC_REQUEST_DONE="sdk_rpc_request_done",e.SDK_EXTENSION_UTILIZED="sdk_extension_utilized",e.SDK_USE_INAPP_BROWSER="sdk_use_inapp_browser"}(lr||(lr={}));const fr=(e,t,n)=>l(void 0,void 0,void 0,(function*(){var r,i,o,s,a,c;const{remote:d,state:l}=e,{channelId:u,isOriginator:h}=l;if("error_terminated"===t)return Yn.SocketService(`handleJoinChannelResults: Channel ${u} terminated`),void e.emit(exports.EventType.TERMINATE);if(!n)return void Yn.SocketService(`handleJoinChannelResults: No result for channel ${u}`);const{persistence:f,walletKey:p,rejected:m}=n;if(Yn.SocketService(`handleJoinChannelResults: Channel ${u} persistence=${f} walletKey=${p} rejected=${m}`),m)return Yn.SocketService(`handleJoinChannelResults: Channel ${u} rejected`),yield e.remote.disconnect({terminate:!0}),e.remote.emit(exports.EventType.REJECTED,{channelId:u}),void e.remote.emitServiceStatusEvent();if(p&&!(null===(r=d.state.channelConfig)||void 0===r?void 0:r.otherKey)){e.getKeyExchange().setOtherPublicKey(p),null===(i=e.state.keyExchange)||void 0===i||i.setKeysExchanged(!0),d.state.ready=!0,d.state.authorized=!0,d.emit(exports.EventType.AUTHORIZED);const{communicationLayer:t,storageManager:n}=d.state,r=Object.assign(Object.assign({},d.state.channelConfig),{channelId:null!==(o=d.state.channelId)&&void 0!==o?o:"",validUntil:Date.now()+ir,localKey:null==t?void 0:t.getKeyInfo().ecies.private,otherKey:p});e.sendMessage({type:dr.KEY_HANDSHAKE_ACK}).catch((e=>{})),null===(s=e.state.socket)||void 0===s||s.emit(exports.MessageType.PING,{id:u,clientType:h?"dapp":"wallet",context:"on_channel_reconnect",message:""}),yield null==n?void 0:n.persistChannelConfig(r),d.emitServiceStatusEvent(),d.setConnectionStatus(exports.ConnectionStatus.LINKED)}f&&(e.emit(exports.EventType.CHANNEL_PERSISTENCE),null===(a=e.state.keyExchange)||void 0===a||a.setKeysExchanged(!0),d.state.ready=!0,d.state.authorized=!0,d.emit(exports.EventType.AUTHORIZED),Xn(Object.assign(Object.assign({id:null!=u?u:"",event:h?lr.CONNECTED:lr.CONNECTED_MOBILE},e.remote.state.originatorInfo),{sdkVersion:e.remote.state.sdkVersion,commLayer:e.state.communicationLayerPreference,commLayerVersion:tr.version,walletVersion:null===(c=e.remote.state.walletInfo)||void 0===c?void 0:c.version}),l.communicationServerUrl).catch((e=>{})))})),pr=e=>new Promise((t=>{setTimeout(t,e)})),mr=(e,t,...n)=>l(void 0,[e,t,...n],void 0,(function*(e,t,n=200){let r;const i=Date.now();let o=!1;for(;!o;){if(o=Date.now()-i>3e5,r=t[e],void 0!==r.elapsedTime)return r;yield pr(n)}throw new Error(`RPC ${e} timed out`)})),gr=e=>l(void 0,void 0,void 0,(function*(){const{state:t}=e,{socket:n,channelId:r,context:i,isOriginator:o,isReconnecting:s}=t;if(s)return Yn.SocketService("[SocketService: reconnectSocket()] Reconnection already in progress, skipping",e),!1;if(!n)return Yn.SocketService("[SocketService: reconnectSocket()] socket is not defined",e),!1;if(!r)return!1;const{connected:a}=n;t.isReconnecting=!0,t.reconnectionAttempts=0,Yn.SocketService(`[SocketService: reconnectSocket()] connected=${a} trying to reconnect after socketio disconnection`,e);try{for(;3>t.reconnectionAttempts;){if(Yn.SocketService(`[SocketService: reconnectSocket()] Attempt ${t.reconnectionAttempts+1} of 3`,e),yield pr(200),n.connected)return Yn.SocketService("Socket already connected --- ping to retrieve messages"),n.emit(exports.MessageType.PING,{id:r,clientType:o?"dapp":"wallet",context:"on_channel_config",message:""}),!0;t.resumed=!0,n.connect(),e.emit(exports.EventType.SOCKET_RECONNECT);try{if(yield new Promise(((t,s)=>{n.emit(exports.EventType.JOIN_CHANNEL,{channelId:r,context:`${i}connect_again`,clientType:o?"dapp":"wallet"},((n,r)=>l(void 0,void 0,void 0,(function*(){try{yield fr(e,n,r),t()}catch(e){s(e)}}))))})),yield pr(100),n.connected)return Yn.SocketService(`Reconnection successful on attempt ${t.reconnectionAttempts+1}`),!0}catch(e){Yn.SocketService(`Error during reconnection attempt ${t.reconnectionAttempts+1}:`,e)}t.reconnectionAttempts+=1,3>t.reconnectionAttempts&&(yield pr(200))}return Yn.SocketService("Failed to reconnect after 3 attempts"),!1}finally{t.isReconnecting=!1,t.reconnectionAttempts=0}}));function vr(e,t){return l(this,void 0,void 0,(function*(){var n;const r=null===(n=e.state.keyExchange)||void 0===n?void 0:n.encryptMessage(JSON.stringify(t)),i={id:e.state.channelId,context:e.state.context,clientType:e.state.isOriginator?"dapp":"wallet",message:r,plaintext:e.state.hasPlaintext?JSON.stringify(t):void 0};return Yn.SocketService(`[SocketService: encryptAndSendMessage()] context=${e.state.context}`,i),t.type===exports.MessageType.TERMINATE&&(e.state.manualDisconnect=!0),new Promise(((t,n)=>{var r;null===(r=e.state.socket)||void 0===r||r.emit(exports.EventType.MESSAGE,i,((e,r)=>{var i;e&&(Yn.SocketService(`[SocketService: encryptAndSendMessage()] error=${e}`),n(e)),Yn.SocketService("[encryptAndSendMessage] response",r),t(null!==(i=null==r?void 0:r.success)&&void 0!==i&&i)}))}))}))}var yr;!function(e){e.RPC_CHECK="rpcCheck",e.SKIPPED_RPC="skippedRpc"}(yr||(yr={}));const Er=["eth_sendTransaction","eth_signTypedData","eth_signTransaction","personal_sign","wallet_requestPermissions","wallet_switchEthereumChain","eth_signTypedData_v3","eth_signTypedData_v4","metamask_connectSign","metamask_connectWith","metamask_batch"].map((e=>e.toLowerCase()));const br=[{event:exports.EventType.CLIENTS_CONNECTED,handler:function(e,t){return n=>l(this,void 0,void 0,(function*(){var n,r,i,o,s,a,c,d,l,u,h;const f=null!==(r=null===(n=e.remote.state.channelConfig)||void 0===n?void 0:n.relayPersistence)&&void 0!==r&&r;if(Yn.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected-${t}' relayPersistence=${f} resumed=${e.state.resumed}  clientsPaused=${e.state.clientsPaused} keysExchanged=${null===(i=e.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()} isOriginator=${e.state.isOriginator}`),e.emit(exports.EventType.CLIENTS_CONNECTED,{isOriginator:e.state.isOriginator,keysExchanged:null===(o=e.state.keyExchange)||void 0===o?void 0:o.areKeysExchanged(),context:e.state.context}),e.state.resumed)e.state.isOriginator||(Yn.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} 'clients_connected' / keysExchanged=${null===(s=e.state.keyExchange)||void 0===s?void 0:s.areKeysExchanged()} -- backward compatibility`),null===(a=e.state.keyExchange)||void 0===a||a.start({isOriginator:null!==(c=e.state.isOriginator)&&void 0!==c&&c})),e.state.resumed=!1;else if(e.state.clientsPaused)Yn.SocketService("[SocketService: handleClientsConnected()] 'clients_connected' skip sending originatorInfo on pause");else if(!e.state.isOriginator){const t=!f;Yn.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected' / keysExchanged=${null===(d=e.state.keyExchange)||void 0===d?void 0:d.areKeysExchanged()} -- force=${t} -- backward compatibility`),Yn.SocketService(`[SocketService: handleClientsConnected()] context=${e.state.context} on 'clients_connected' / keysExchanged=${null===(l=e.state.keyExchange)||void 0===l?void 0:l.areKeysExchanged()} -- force=${t} -- backward compatibility`),null===(u=e.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=e.state.isOriginator)&&void 0!==h&&h,force:t})}e.state.clientsConnected=!0,e.state.clientsPaused=!1}))}},{event:exports.EventType.CHANNEL_CREATED,handler:function(e,t){return n=>{Yn.SocketService(`[SocketService: handleChannelCreated()] context=${e.state.context} on 'channel_created-${t}'`,n),e.emit(exports.EventType.CHANNEL_CREATED,n)}}},{event:exports.EventType.CLIENTS_DISCONNECTED,handler:function(e,t){return()=>{var n;e.state.clientsConnected=!1,Yn.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}'`),e.remote.state.relayPersistence?Yn.SocketService(`[SocketService: handlesClientsDisconnected()] context=${e.state.context} on 'clients_disconnected-${t}' - relayPersistence enabled, skipping key exchange cleanup.`):(e.state.isOriginator&&!e.state.clientsPaused&&(null===(n=e.state.keyExchange)||void 0===n||n.clean()),e.emit(exports.EventType.CLIENTS_DISCONNECTED,t))}}},{event:exports.EventType.CONFIG,handler:function(e,t){return n=>l(this,void 0,void 0,(function*(){var r,i,o;Yn.SocketService(`[SocketService: handleChannelConfig()] update relayPersistence on 'config-${t}'`,n);const{persistence:s,walletKey:a}=n;e.state.isOriginator&&e.remote.state.channelConfig?(n.walletKey&&!e.remote.state.channelConfig.otherKey&&(Yn.SocketService(`Setting wallet key ${a}`),e.remote.state.channelConfig.otherKey=a,e.getKeyExchange().setOtherPublicKey(n.walletKey),null===(r=e.state.keyExchange)||void 0===r||r.setKeysExchanged(!0),yield e.remote.sendMessage({type:dr.KEY_HANDSHAKE_ACK}),yield e.remote.sendMessage({type:exports.MessageType.PING}),yield null===(i=e.remote.state.storageManager)||void 0===i?void 0:i.persistChannelConfig(e.remote.state.channelConfig)),!0!==s||e.remote.state.channelConfig.relayPersistence||(Yn.SocketService(`Setting relay persistence ${s}`),e.remote.state.channelConfig.relayPersistence=s,e.remote.state.relayPersistence=!0,e.remote.emit(exports.EventType.CHANNEL_PERSISTENCE),e.remote.state.authorized=!0,e.remote.state.ready=!0,e.remote.emit(exports.EventType.AUTHORIZED),yield null===(o=e.remote.state.storageManager)||void 0===o?void 0:o.persistChannelConfig(e.remote.state.channelConfig))):e.state.isOriginator||n.persistence&&(e.remote.state.relayPersistence=!0,e.remote.emit(exports.EventType.CHANNEL_PERSISTENCE))}))}},{event:exports.EventType.MESSAGE,handler:function(e,t){return n=>{var r,i,o,s,a,c,d,l,u,h,f,p,m,g,v,y,E,b;const{ackId:w,message:S,error:_}=n,C=null!==(r=e.remote.state.relayPersistence)&&void 0!==r&&r;if(Yn.SocketService(`[SocketService handleMessage()]  relayPersistence=${C}  context=${e.state.context} on 'message' ${t} keysExchanged=${null===(i=e.state.keyExchange)||void 0===i?void 0:i.areKeysExchanged()}`,n),_)throw Yn.SocketService(`\n      [SocketService handleMessage()] context=${e.state.context}::on 'message' error=${_}`),new Error(_);const x="string"==typeof S;if(!x&&(null==S?void 0:S.type)===dr.KEY_HANDSHAKE_START){if(C)return;return Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received HANDSHAKE_START isOriginator=${e.state.isOriginator}`,S),void(null===(o=e.state.keyExchange)||void 0===o||o.start({isOriginator:null!==(s=e.state.isOriginator)&&void 0!==s&&s,force:!0}))}if(!x&&(null===(a=null==S?void 0:S.type)||void 0===a?void 0:a.startsWith("key_handshake"))){if(C)return;return Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' emit KEY_EXCHANGE`,S),void e.emit(cr.KEY_EXCHANGE,{message:S,context:e.state.context})}if(x&&!(null===(c=e.state.keyExchange)||void 0===c?void 0:c.areKeysExchanged())){let t=!1;try{Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' trying to decrypt message`),null===(d=e.state.keyExchange)||void 0===d||d.decryptMessage(S),t=!0}catch(t){Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' error`,t)}if(!t)return e.state.isOriginator?null===(u=e.state.keyExchange)||void 0===u||u.start({isOriginator:null!==(h=e.state.isOriginator)&&void 0!==h&&h}):e.sendMessage({type:dr.KEY_HANDSHAKE_START}).catch((e=>{})),void Yn.SocketService(`Message ignored because invalid key exchange status. step=${null===(f=e.state.keyExchange)||void 0===f?void 0:f.getKeyInfo().step}`,null===(p=e.state.keyExchange)||void 0===p?void 0:p.getKeyInfo(),S);Yn.SocketService("Invalid key exchange status detected --- updating it."),null===(l=e.state.keyExchange)||void 0===l||l.setKeysExchanged(!0)}else if(!x&&(null==S?void 0:S.type))return void e.emit(exports.EventType.MESSAGE,S);if(!x)return void e.emit(exports.EventType.MESSAGE,S);const k=null===(m=e.state.keyExchange)||void 0===m?void 0:m.decryptMessage(S),M=JSON.parse(null!=k?k:"{}");if(w&&(null==w?void 0:w.length)>0&&(Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' ackid=${w} channelId=${t}`),null===(g=e.state.socket)||void 0===g||g.emit(exports.EventType.MESSAGE_ACK,{ackId:w,channelId:t,clientType:e.state.isOriginator?"dapp":"wallet"})),e.state.clientsPaused=(null==M?void 0:M.type)===exports.MessageType.PAUSE,e.state.isOriginator&&M.data){const t=M.data,n=e.state.rpcMethodTracker[t.id];if(n){const r=Date.now()-n.timestamp;Yn.SocketService(`[SocketService handleMessage()] context=${e.state.context}::on 'message' received answer for id=${t.id} method=${n.method} responseTime=${r}`,M),e.remote.state.analytics&&Er.includes(n.method.toLowerCase())&&Xn(Object.assign(Object.assign({id:null!==(v=e.remote.state.channelId)&&void 0!==v?v:"",event:lr.SDK_RPC_REQUEST_DONE,sdkVersion:e.remote.state.sdkVersion,commLayerVersion:tr.version},e.remote.state.originatorInfo),{walletVersion:null===(y=e.remote.state.walletInfo)||void 0===y?void 0:y.version,params:{method:n.method,from:"mobile"}}),e.remote.state.communicationServerUrl).catch((e=>{}));const i=Object.assign(Object.assign({},n),{result:t.result,error:t.error?{code:null===(E=t.error)||void 0===E?void 0:E.code,message:null===(b=t.error)||void 0===b?void 0:b.message}:void 0,elapsedTime:r});e.state.rpcMethodTracker[t.id]=i,e.emit(exports.EventType.RPC_UPDATE,i)}}e.emit(exports.EventType.MESSAGE,{message:M})}}},{event:exports.EventType.REJECTED,handler:function(e,t){return n=>l(this,void 0,void 0,(function*(){var n;e.state.isOriginator&&!e.remote.state.ready?(Yn.SocketService(`[SocketService: handleChannelRejected()] context=${e.state.context} channelId=${t} isOriginator=${e.state.isOriginator} ready=${e.remote.state.ready}`,e.remote.state.originatorInfo),Xn(Object.assign(Object.assign({id:t,event:lr.REJECTED},e.remote.state.originatorInfo),{sdkVersion:e.remote.state.sdkVersion,commLayer:e.state.communicationLayerPreference,commLayerVersion:tr.version,walletVersion:null===(n=e.remote.state.walletInfo)||void 0===n?void 0:n.version}),e.remote.state.communicationServerUrl).catch((e=>{})),yield e.remote.disconnect({terminate:!0}),e.remote.emit(exports.EventType.REJECTED,{channelId:t}),e.remote.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED)):Yn.SocketService(`[SocketService: handleChannelRejected()] SKIP -- channelId=${t} isOriginator=${e.state.isOriginator} ready=${e.remote.state.ready}`)}))}},{event:"clients_waiting_to_join",handler:function(e,t){return n=>{Yn.SocketService(`[SocketService: handleClientsWaitingToJoin()] context=${e.state.context} on 'clients_waiting_to_join-${t}'`,n),e.emit(exports.EventType.CLIENTS_WAITING,n)}}}],wr=[{event:exports.EventType.KEY_INFO,handler:function(e){return t=>{Yn.SocketService("[SocketService: handleKeyInfo()] on 'KEY_INFO'",t),e.emit(exports.EventType.KEY_INFO,t)}}},{event:exports.EventType.KEYS_EXCHANGED,handler:function(e){return()=>{var t,n,r;Yn.SocketService(`[SocketService: handleKeysExchanged()] on 'keys_exchanged' keyschanged=${null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged()}`);const{channelConfig:i}=e.remote.state;if(i){const t=e.getKeyExchange().getKeyInfo().ecies;i.localKey=t.private,i.otherKey=t.otherPubKey,e.remote.state.channelConfig=i,null===(n=e.remote.state.storageManager)||void 0===n||n.persistChannelConfig(i).catch((e=>{}))}e.emit(exports.EventType.KEYS_EXCHANGED,{keysExchanged:null===(r=e.state.keyExchange)||void 0===r?void 0:r.areKeysExchanged(),isOriginator:e.state.isOriginator});const o={keyInfo:e.getKeyInfo()};e.emit(exports.EventType.SERVICE_STATUS,o)}}}];function Sr(e,t){Yn.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} setting socket listeners for channel ${t}...`);const{socket:n}=e.state,{keyExchange:r}=e.state;n&&e.state.isOriginator&&(e.state.debug&&(null==n||n.io.on("error",(t=>{Yn.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=error`,t)})),null==n||n.io.on("reconnect",(t=>{Yn.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect`,t),gr(e).catch((e=>{}))})),null==n||n.io.on("reconnect_error",(t=>{Yn.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_error`,t)})),null==n||n.io.on("reconnect_failed",(()=>{Yn.SocketService(`[SocketService: setupChannelListener()] context=${e.state.context} socket event=reconnect_failed`)}))),null==n||n.on("disconnect",(t=>(Yn.SocketService(`[SocketService: setupChannelListener()] on 'disconnect' -- MetaMaskSDK socket disconnected '${t}' begin recovery...`),function(e){return t=>{Yn.SocketService(`[SocketService: handleDisconnect()] on 'disconnect' manualDisconnect=${e.state.manualDisconnect}`,t),e.state.manualDisconnect||(e.emit(exports.EventType.SOCKET_DISCONNECTED),gr(e).catch((e=>{})))}}(e)(t))))),br.forEach((({event:r,handler:i})=>{null==n||n.on(`${r}-${t}`,i(e,t))})),wr.forEach((({event:t,handler:n})=>{null==r||r.on(t,n(e))})),e.state.setupChannelListeners=!0}class _r extends i.EventEmitter2{constructor(e){super(),this.state={clientsConnected:!1,clientsPaused:!1,manualDisconnect:!1,lastRpcId:void 0,rpcMethodTracker:{},hasPlaintext:!1,communicationServerUrl:"",focusListenerAdded:!1,removeFocusListener:void 0,isReconnecting:!1,reconnectionAttempts:0},this.options=e;const{reconnect:n,communicationLayerPreference:r,communicationServerUrl:i,context:o,remote:s,logging:a}=e;this.state.resumed=n,this.state.context=o,this.state.isOriginator=s.state.isOriginator,this.state.communicationLayerPreference=r,this.state.debug=!0===(null==a?void 0:a.serviceLayer),this.remote=s,!0===(null==a?void 0:a.serviceLayer)&&t.enable("SocketService:Layer"),this.state.communicationServerUrl=i,this.state.hasPlaintext=this.state.communicationServerUrl!==nr&&!0===(null==a?void 0:a.plaintext),Yn.SocketService(`[SocketService: constructor()] Socket IO url: ${this.state.communicationServerUrl}`),this.initSocket()}initSocket(){var e;const{otherPublicKey:t,ecies:n,logging:r}=this.options,i={autoConnect:!1,transports:rr,withCredentials:!0},o=this.state.communicationServerUrl;Yn.SocketService(`[SocketService: initSocket()] Socket IO url: ${o}`),this.state.socket=s.io(o,i),function(e){if("undefined"!=typeof window&&"undefined"!=typeof document&&(Yn.SocketService(`[SocketService: setupSocketFocusListener()] hasFocus=${document.hasFocus()}`,e),!e.state.focusListenerAdded)){const t=()=>{Yn.SocketService("Document has focus --- reconnecting socket"),gr(e).catch((e=>{}))};window.addEventListener("focus",t),e.state.focusListenerAdded=!0,e.state.removeFocusListener=()=>{window.removeEventListener("focus",t),e.state.focusListenerAdded=!1}}}(this);const a={communicationLayer:this,otherPublicKey:t,sendPublicKey:!1,context:null!==(e=this.state.context)&&void 0!==e?e:"",ecies:n,logging:r};this.state.keyExchange=new hr(a)}resetKeys(){return Yn.SocketService("[SocketService: resetKeys()] Resetting keys."),void(null===(e=this.state.keyExchange)||void 0===e||e.resetKeys());var e}createChannel(){return l(this,void 0,void 0,(function*(){return function(e){return l(this,void 0,void 0,(function*(){var t,n,r;if(Yn.SocketService(`[SocketService: createChannel()] context=${e.state.context}`),e.state.socket||e.initSocket(),null===(t=e.state.socket)||void 0===t?void 0:t.connected)throw new Error("socket already connected");null===(n=e.state.socket)||void 0===n||n.connect(),e.state.manualDisconnect=!1,e.state.isOriginator=!0;const i=o.v4();e.state.channelId=i,Sr(e,i),yield new Promise(((t,n)=>{var r;null===(r=e.state.socket)||void 0===r||r.emit(exports.EventType.JOIN_CHANNEL,{channelId:i,context:`${e.state.context}createChannel`,clientType:"dapp"},((r,i)=>l(this,void 0,void 0,(function*(){try{yield fr(e,r,i),t()}catch(e){n(e)}}))))}));const s=null===(r=e.state.keyExchange)||void 0===r?void 0:r.getKeyInfo();return{channelId:i,pubKey:(null==s?void 0:s.ecies.public)||"",privKey:(null==s?void 0:s.ecies.private)||""}}))}(this)}))}connectToChannel({channelId:e,withKeyExchange:t=!1,authorized:n}){return function(e){return l(this,arguments,void 0,(function*({options:e,instance:t}){const{channelId:n,authorized:r,withKeyExchange:i}=e,{state:o,remote:s}=t,{isOriginator:a=!1,socket:c,keyExchange:d}=o,{channelConfig:u}=s.state;if(null==c?void 0:c.connected)throw new Error("socket already connected");if(a&&(null==u?void 0:u.relayPersistence)){const{localKey:e,otherKey:t}=u;e&&t&&(null==d||d.setRelayPersistence({localKey:e,otherKey:t}))}return Object.assign(o,{manualDisconnect:!1,withKeyExchange:i,isOriginator:a,channelId:n}),null==c||c.connect(),Sr(t,n),!a&&r&&(null==d||d.setKeysExchanged(!0),Object.assign(s.state,{ready:!0,authorized:!0})),new Promise((e=>{var i;const s=null===(i=null==d?void 0:d.getKeyInfo())||void 0===i?void 0:i.ecies.public;null==c||c.emit(exports.EventType.JOIN_CHANNEL,{channelId:n,context:`${o.context}_connectToChannel`,clientType:a?"dapp":"wallet",publicKey:r&&!a?s:void 0},((n,r)=>l(this,void 0,void 0,(function*(){yield fr(t,n,r),e()}))))}))}))}({options:{channelId:e,withKeyExchange:t,authorized:n},instance:this})}getKeyInfo(){return this.state.keyExchange.getKeyInfo()}keyCheck(){var e,t;null===(t=(e=this).state.socket)||void 0===t||t.emit(exports.EventType.MESSAGE,{id:e.state.channelId,context:e.state.context,message:{type:dr.KEY_HANDSHAKE_CHECK,pubkey:e.getKeyInfo().ecies.otherPubKey}})}getKeyExchange(){return this.state.keyExchange}sendMessage(e){return l(this,void 0,void 0,(function*(){return function(e,t){return l(this,void 0,void 0,(function*(){var n,r,i;if(!e.state.channelId)throw Yn.SocketService("handleSendMessage: no channelId - Create a channel first"),new Error("Create a channel first");if(Yn.SocketService(`[SocketService: handleSendMessage()] context=${e.state.context} areKeysExchanged=${null===(n=e.state.keyExchange)||void 0===n?void 0:n.areKeysExchanged()}`,t),null===(r=null==t?void 0:t.type)||void 0===r?void 0:r.startsWith("key_handshake"))return function(e,t){var n;Yn.SocketService(`[SocketService: handleKeyHandshake()] context=${e.state.context}`,t),null===(n=e.state.socket)||void 0===n||n.emit(exports.EventType.MESSAGE,{id:e.state.channelId,context:e.state.context,clientType:e.state.isOriginator?"dapp":"wallet",message:t})}(e,t),!0;!function(e,t){var n;if(!(null===(n=e.state.keyExchange)||void 0===n?void 0:n.areKeysExchanged())&&!e.remote.state.relayPersistence)throw Yn.SocketService(`[SocketService: validateKeyExchange()] context=${e.state.context} ERROR keys not exchanged`,t),new Error("Keys not exchanged BBB")}(e,t),function(e,t){var n;const r=null!==(n=null==t?void 0:t.method)&&void 0!==n?n:"",i=null==t?void 0:t.id;e.state.isOriginator&&i&&(e.state.rpcMethodTracker[i]={id:i,timestamp:Date.now(),method:r},e.emit(exports.EventType.RPC_UPDATE,e.state.rpcMethodTracker[i]))}(e,t);const o=yield vr(e,t);return e.remote.state.analytics&&e.remote.state.isOriginator&&t.method&&Er.includes(t.method.toLowerCase())&&Xn({id:null!==(i=e.remote.state.channelId)&&void 0!==i?i:"",event:lr.SDK_RPC_REQUEST,params:{method:t.method,from:"mobile"}},e.remote.state.communicationServerUrl).catch((e=>{})),function(e,t){return l(this,void 0,void 0,(function*(){var n;const r=null==t?void 0:t.id,i=null!==(n=null==t?void 0:t.method)&&void 0!==n?n:"";if(e.state.isOriginator&&r)try{const n=mr(r,e.state.rpcMethodTracker,200).then((e=>({type:yr.RPC_CHECK,result:e}))),o=(()=>l(this,void 0,void 0,(function*(){const t=yield(e=>l(void 0,[e],void 0,(function*({rpcId:e,instance:t}){for(;t.state.lastRpcId===e||void 0===t.state.lastRpcId;)yield pr(200);return t.state.lastRpcId})))({instance:e,rpcId:r}),n=yield mr(t,e.state.rpcMethodTracker,200);return{type:yr.SKIPPED_RPC,result:n}})))(),s=yield Promise.race([n,o]);if(s.type===yr.RPC_CHECK){const e=s.result;Yn.SocketService(`[SocketService:handleRpcReplies()] id=${t.id} ${i} ( ${e.elapsedTime} ms)`,e.result)}else{if(s.type!==yr.SKIPPED_RPC)throw new Error(`Error handling RPC replies for ${r}`);{const t=Object.assign(Object.assign({},e.state.rpcMethodTracker[r]),{error:new Error("SDK_CONNECTION_ISSUE")});e.emit(exports.EventType.RPC_UPDATE,t);const n={data:Object.assign(Object.assign({},t),{jsonrpc:"2.0"}),name:"metamask-provider"};e.emit(exports.EventType.MESSAGE,{message:n})}}}catch(e){throw e}}))}(e,t).catch((e=>{})),o}))}(this,e)}))}ping(){return function(e){return l(this,void 0,void 0,(function*(){var t,n;Yn.SocketService(`[SocketService: ping()] context=${e.state.context} originator=${e.state.isOriginator} keysExchanged=${null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged()}`),null===(n=e.state.socket)||void 0===n||n.emit(exports.MessageType.PING,{id:e.state.channelId,context:"ping",clientType:e.remote.state.isOriginator?"dapp":"wallet",message:""})}))}(this)}pause(){return function(e){return l(this,void 0,void 0,(function*(){var t,n;Yn.SocketService(`[SocketService: pause()] context=${e.state.context}`),e.state.manualDisconnect=!0,(null===(t=e.state.keyExchange)||void 0===t?void 0:t.areKeysExchanged())&&(yield e.sendMessage({type:exports.MessageType.PAUSE})),null===(n=e.state.socket)||void 0===n||n.disconnect()}))}(this)}isConnected(){var e;return null===(e=this.state.socket)||void 0===e?void 0:e.connected}resume(){return function(e){return l(this,void 0,void 0,(function*(){const{state:t,remote:n}=e,{socket:r,channelId:i,context:o,keyExchange:s,isOriginator:a}=t,{isOriginator:c}=n.state;if(Yn.SocketService(`[SocketService: resume()] channelId=${i} context=${o} connected=${null==r?void 0:r.connected} manualDisconnect=${t.manualDisconnect} resumed=${t.resumed} keysExchanged=${null==s?void 0:s.areKeysExchanged()}`),!i)throw Yn.SocketService("[SocketService: resume()] channelId is not defined"),new Error("ChannelId is not defined");(null==r?void 0:r.connected)?(Yn.SocketService("[SocketService: resume()] already connected."),r.emit(exports.MessageType.PING,{id:i,clientType:c?"dapp":"wallet",context:"on_channel_config",message:""}),n.hasRelayPersistence()||(null==s?void 0:s.areKeysExchanged())||(a?yield e.sendMessage({type:exports.MessageType.READY}):null==s||s.start({isOriginator:!1}))):(null==r||r.connect(),Yn.SocketService(`[SocketService: resume()] after connecting socket --\x3e connected=${null==r?void 0:r.connected}`),null==r||r.emit(exports.EventType.JOIN_CHANNEL,{channelId:i,context:`${o}_resume`,clientType:c?"dapp":"wallet"},((t,n)=>l(this,void 0,void 0,(function*(){try{yield fr(e,t,n)}catch(e){}}))))),t.manualDisconnect=!1,t.resumed=!0}))}(this)}getRPCMethodTracker(){return this.state.rpcMethodTracker}disconnect(e){return function(e,t){var n,r,i,o,s;Yn.SocketService(`[SocketService: disconnect()] context=${e.state.context}`,t),(null==t?void 0:t.terminate)&&(null===(r=(n=e.state).removeFocusListener)||void 0===r||r.call(n),e.state.channelId=t.channelId,null===(i=e.state.socket)||void 0===i||i.removeAllListeners(),null===(o=e.state.keyExchange)||void 0===o||o.clean(),e.remote.state.ready=!1,e.state.socket=void 0,e.state.rpcMethodTracker={}),e.state.manualDisconnect=!0,null===(s=e.state.socket)||void 0===s||s.disconnect()}(this,e)}}var Cr;function xr(e){return()=>l(this,void 0,void 0,(function*(){var t,n,r;const{state:i}=e;if(i.authorized)return;yield(()=>l(this,void 0,void 0,(function*(){for(;!i.walletInfo;)yield pr(500)})))();const o="7.3".localeCompare((null===(t=i.walletInfo)||void 0===t?void 0:t.version)||"");if(Yn.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' version=${null===(n=i.walletInfo)||void 0===n?void 0:n.version} compareValue=${o}`),1!==o)return;const s=i.platformType===exports.PlatformType.MobileWeb||i.platformType===exports.PlatformType.ReactNative||i.platformType===exports.PlatformType.MetaMaskMobileWebview;Yn.RemoteCommunication(`[RemoteCommunication: handleAuthorizedEvent()] HACK 'authorized' platform=${i.platformType} secure=${s} channel=${i.channelId} walletVersion=${null===(r=i.walletInfo)||void 0===r?void 0:r.version}`),s&&(i.authorized=!0,e.emit(exports.EventType.AUTHORIZED))}))}function kr(e){return t=>{const{state:n}=e;Yn.RemoteCommunication(`[RemoteCommunication: handleChannelCreatedEvent()] context=${n.context} on 'channel_created' channelId=${t}`),e.emit(exports.EventType.CHANNEL_CREATED,t)}}function Mr(e,t){return()=>{var n,r,i,o;const{state:s}=e;Yn.RemoteCommunication(`[RemoteCommunication: handleClientsConnectedEvent()] on 'clients_connected' channel=${s.channelId} keysExchanged=${null===(r=null===(n=s.communicationLayer)||void 0===n?void 0:n.getKeyInfo())||void 0===r?void 0:r.keysExchanged}`),s.analytics&&Xn(Object.assign(Object.assign({id:null!==(i=s.channelId)&&void 0!==i?i:"",event:s.reconnection?lr.RECONNECT:s.isOriginator?lr.REQUEST:lr.REQUEST_MOBILE},s.originatorInfo),{commLayer:t,sdkVersion:s.sdkVersion,walletVersion:null===(o=s.walletInfo)||void 0===o?void 0:o.version,commLayerVersion:tr.version}),s.communicationServerUrl).catch((e=>{})),s.clientsConnected=!0,s.originatorInfoSent=!1,e.emit(exports.EventType.CLIENTS_CONNECTED)}}function Ar(e){return t=>{const{state:n}=e;Yn.RemoteCommunication(`[RemoteCommunication: handleClientsDisconnectedEvent()] context=${n.context} on 'clients_disconnected' channelId=${t}`),n.relayPersistence||(n.clientsConnected=!1,n.ready=!1,n.authorized=!1),e.emit(exports.EventType.CLIENTS_DISCONNECTED,n.channelId),e.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED)}}function Tr(e){return t=>{var n;const{state:r}=e;if(Yn.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] context=${r.context} on 'clients_waiting' numberUsers=${t} ready=${r.ready} autoStarted=${r.originatorConnectStarted}`),e.setConnectionStatus(exports.ConnectionStatus.WAITING),e.emit(exports.EventType.CLIENTS_WAITING,t),r.originatorConnectStarted){Yn.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] on 'clients_waiting' watch autoStarted=${r.originatorConnectStarted} timeout`,r.autoConnectOptions);const t=(null===(n=r.autoConnectOptions)||void 0===n?void 0:n.timeout)||3e3,i=setTimeout((()=>{Yn.RemoteCommunication(`[RemoteCommunication: handleClientsWaitingEvent()] setTimeout(${t}) terminate channelConfig`,r.autoConnectOptions),r.originatorConnectStarted=!1,r.ready||e.setConnectionStatus(exports.ConnectionStatus.TIMEOUT),clearTimeout(i)}),t)}}}function Ir(e,t){return n=>{var r,i,o,s,a,c,d,l;const{state:u}=e;if(Yn.RemoteCommunication(`[RemoteCommunication: handleKeysExchangedEvent()] context=${u.context} on commLayer.'keys_exchanged' channel=${u.channelId}`,n),null===(i=null===(r=u.communicationLayer)||void 0===r?void 0:r.getKeyInfo())||void 0===i?void 0:i.keysExchanged){const t=Object.assign(Object.assign({},u.channelConfig),{channelId:null!==(o=u.channelId)&&void 0!==o?o:"",validUntil:(null===(s=u.channelConfig)||void 0===s?void 0:s.validUntil)||ir,localKey:u.communicationLayer.getKeyInfo().ecies.private,otherKey:u.communicationLayer.getKeyInfo().ecies.otherPubKey});null===(a=u.storageManager)||void 0===a||a.persistChannelConfig(t).catch((e=>{})),e.setConnectionStatus(exports.ConnectionStatus.LINKED)}!function(e,t){var n,r,i,o,s,a,c,d;const{state:l}=e;Yn.RemoteCommunication(`[RemoteCommunication: setLastActiveDate()] channel=${l.channelId}`,t);const u=Object.assign(Object.assign({},l.channelConfig),{channelId:null!==(n=l.channelId)&&void 0!==n?n:"",validUntil:null!==(i=null===(r=l.channelConfig)||void 0===r?void 0:r.validUntil)&&void 0!==i?i:0,relayPersistence:l.relayPersistence,localKey:null===(s=null===(o=l.communicationLayer)||void 0===o?void 0:o.state.keyExchange)||void 0===s?void 0:s.getKeyInfo().ecies.private,otherKey:null===(c=null===(a=l.communicationLayer)||void 0===a?void 0:a.state.keyExchange)||void 0===c?void 0:c.getKeyInfo().ecies.otherPubKey,lastActive:t.getTime()});null===(d=l.storageManager)||void 0===d||d.persistChannelConfig(u)}(e,new Date),u.analytics&&u.channelId&&Xn(Object.assign(Object.assign({id:u.channelId,event:n.isOriginator?lr.CONNECTED:lr.CONNECTED_MOBILE},u.originatorInfo),{sdkVersion:u.sdkVersion,commLayer:t,commLayerVersion:tr.version,walletVersion:null===(c=u.walletInfo)||void 0===c?void 0:c.version}),u.communicationServerUrl).catch((e=>{})),u.isOriginator=n.isOriginator,n.isOriginator||(null===(d=u.communicationLayer)||void 0===d||d.sendMessage({type:exports.MessageType.READY}),u.ready=!0,u.paused=!1),n.isOriginator&&!u.originatorInfoSent&&(null===(l=u.communicationLayer)||void 0===l||l.sendMessage({type:exports.MessageType.ORIGINATOR_INFO,originatorInfo:u.originatorInfo,originator:u.originatorInfo}),u.originatorInfoSent=!0)}}function Pr(e){return t=>{let n=t;t.message&&(n=n.message),function(e,t){const{state:n}=t;if(Yn.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] context=${n.context} on 'message' typeof=${typeof e}`,e),t.state.ready=!0,n.isOriginator||e.type!==exports.MessageType.ORIGINATOR_INFO)if(n.isOriginator&&e.type===exports.MessageType.WALLET_INFO)!function(e,t){const{state:n}=e;n.walletInfo=t.walletInfo,n.paused=!1}(t,e);else{if(n.isOriginator&&e.type===exports.MessageType.WALLET_INIT)(function(e,t){return l(this,void 0,void 0,(function*(){var n,r,i;const{state:o}=e;if(o.isOriginator){const o=t.data||{};if("object"==typeof o&&"accounts"in o&&"chainId"in o&&"walletKey"in o)try{const{channelConfig:t}=e.state;if(Yn.RemoteCommunication("WALLET_INIT: channelConfig",JSON.stringify(t,null,2)),t){const s=o.accounts,a=o.chainId,c=o.walletKey;let d,l=!1;"deeplinkProtocol"in o&&(l=Boolean(o.deeplinkProtocol),e.state.deeplinkProtocolAvailable=l),"walletVersion"in o&&(d=o.walletVersion),yield null===(n=e.state.storageManager)||void 0===n?void 0:n.persistChannelConfig(Object.assign(Object.assign({},t),{otherKey:c,walletVersion:d,deeplinkProtocolAvailable:l,relayPersistence:!0})),yield null===(r=e.state.storageManager)||void 0===r?void 0:r.persistAccounts(s),yield null===(i=e.state.storageManager)||void 0===i?void 0:i.persistChainId(a)}e.emit(exports.EventType.WALLET_INIT,{accounts:o.accounts,chainId:o.chainId})}catch(n){}}}))})(t,e).catch((e=>{Yn.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(e.type===exports.MessageType.TERMINATE)(function(e){return l(this,void 0,void 0,(function*(){const{state:t}=e;t.isOriginator&&(yield Lr({options:{terminate:!0,sendMessage:!1},instance:e}),e.emit(exports.EventType.TERMINATE))}))})(t).catch((e=>{Yn.RemoteCommunication(`[RemoteCommunication: onCommunicationLayerMessage()] error=${e}`)}));else if(e.type===exports.MessageType.PAUSE)!function(e){const{state:t}=e;t.paused=!0,e.setConnectionStatus(exports.ConnectionStatus.PAUSED)}(t);else if(e.type===exports.MessageType.READY&&n.isOriginator)!function(e){const{state:t}=e;e.setConnectionStatus(exports.ConnectionStatus.LINKED);const n=t.paused;t.paused=!1,e.emit(exports.EventType.CLIENTS_READY,{isOriginator:t.isOriginator,walletInfo:t.walletInfo}),n&&(t.authorized=!0,e.emit(exports.EventType.AUTHORIZED))}(t);else{if(e.type===exports.MessageType.OTP&&n.isOriginator)return void function(e,t){var n;const{state:r}=e;e.emit(exports.EventType.OTP,t.otpAnswer),1==="6.6".localeCompare((null===(n=r.walletInfo)||void 0===n?void 0:n.version)||"")&&e.emit(exports.EventType.SDK_RPC_CALL,{method:sr.ETH_REQUESTACCOUNTS,params:[]})}(t,e);e.type===exports.MessageType.AUTHORIZED&&n.isOriginator&&function(e){const{state:t}=e;t.authorized=!0,e.emit(exports.EventType.AUTHORIZED)}(t)}t.emit(exports.EventType.MESSAGE,e)}else!function(e,t){var n;const{state:r}=e;null===(n=r.communicationLayer)||void 0===n||n.sendMessage({type:exports.MessageType.WALLET_INFO,walletInfo:r.walletInfo}),r.originatorInfo=t.originatorInfo||t.originator,e.emit(exports.EventType.CLIENTS_READY,{isOriginator:r.isOriginator,originatorInfo:r.originatorInfo}),r.paused=!1}(t,e)}(n,e)}}function Rr(e){return()=>{const{state:t}=e;Yn.RemoteCommunication("[RemoteCommunication: handleSocketReconnectEvent()] on 'socket_reconnect' -- reset key exchange status / set ready to false"),t.ready=!1,t.authorized=!1,ar(t),e.emitServiceStatusEvent({context:"socket_reconnect"})}}function Or(e){return()=>{const{state:t}=e;Yn.RemoteCommunication("[RemoteCommunication: handleSocketDisconnectedEvent()] on 'socket_Disconnected' set ready to false"),t.ready=!1}}function Nr(e){return()=>l(this,void 0,void 0,(function*(){var t,n,r,i,o,s,a;const{state:c}=e;Yn.RemoteCommunication(`[RemoteCommunication: handleFullPersistenceEvent()] context=${c.context}`),e.state.ready=!0,e.state.clientsConnected=!0,e.state.authorized=!0,e.state.relayPersistence=!0,null===(t=e.state.communicationLayer)||void 0===t||t.getKeyExchange().setKeysExchanged(!0),e.emit(exports.EventType.KEYS_EXCHANGED,{keysExchanged:!0,isOriginator:!0}),e.emit(exports.EventType.AUTHORIZED),e.emit(exports.EventType.CLIENTS_READY),e.emit(exports.EventType.CHANNEL_PERSISTENCE);try{c.channelConfig=Object.assign(Object.assign({},c.channelConfig),{localKey:null===(n=c.communicationLayer)||void 0===n?void 0:n.getKeyExchange().getKeyInfo().ecies.private,otherKey:null===(r=c.communicationLayer)||void 0===r?void 0:r.getKeyExchange().getOtherPublicKey(),channelId:null!==(i=c.channelId)&&void 0!==i?i:"",validUntil:null!==(s=null===(o=c.channelConfig)||void 0===o?void 0:o.validUntil)&&void 0!==s?s:ir,relayPersistence:!0}),yield null===(a=c.storageManager)||void 0===a?void 0:a.persistChannelConfig(c.channelConfig)}catch(t){}}))}function Dr({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,ecies:r,communicationServerUrl:i=nr,instance:o}){var s,a,c,d,l,u,h,f,p,m,g;const{state:v}=o;if(Yn.RemoteCommunication("[initCommunicationLayer()] ",JSON.stringify(v,null,2)),e!==exports.CommunicationLayerPreference.SOCKET)throw new Error("Invalid communication protocol");v.communicationLayer=new _r({communicationLayerPreference:e,otherPublicKey:t,reconnect:n,transports:v.transports,communicationServerUrl:i,context:v.context,ecies:r,logging:v.logging,remote:o});let y="undefined"!=typeof document&&document.URL||"",E="undefined"!=typeof document&&document.title||"";(null===(s=v.dappMetadata)||void 0===s?void 0:s.url)&&(y=v.dappMetadata.url),(null===(a=v.dappMetadata)||void 0===a?void 0:a.name)&&(E=v.dappMetadata.name);const b=null!==(u=null!==(d=null===(c=v.dappMetadata)||void 0===c?void 0:c.name)&&void 0!==d?d:null===(l=v.dappMetadata)||void 0===l?void 0:l.url)&&void 0!==u?u:"N/A",w="undefined"!=typeof window&&void 0!==window.location&&null!==(h=window.location.hostname)&&void 0!==h?h:b,S={url:y,title:E,source:null===(f=v.dappMetadata)||void 0===f?void 0:f.source,dappId:w,icon:(null===(p=v.dappMetadata)||void 0===p?void 0:p.iconUrl)||(null===(m=v.dappMetadata)||void 0===m?void 0:m.base64Icon),platform:v.platformType,apiVersion:tr.version,connector:null===(g=v.dappMetadata)||void 0===g?void 0:g.connector};v.originatorInfo=S;const _={[exports.EventType.AUTHORIZED]:xr(o),[exports.EventType.MESSAGE]:Pr(o),[exports.EventType.CHANNEL_PERSISTENCE]:Nr(o),[exports.EventType.CLIENTS_CONNECTED]:Mr(o,e),[exports.EventType.KEYS_EXCHANGED]:Ir(o,e),[exports.EventType.SOCKET_DISCONNECTED]:Or(o),[exports.EventType.SOCKET_RECONNECT]:Rr(o),[exports.EventType.CLIENTS_DISCONNECTED]:Ar(o),[exports.EventType.KEY_INFO]:()=>{},[exports.EventType.CHANNEL_CREATED]:kr(o),[exports.EventType.CLIENTS_WAITING]:Tr(o),[exports.EventType.RPC_UPDATE]:e=>{o.emit(exports.EventType.RPC_UPDATE,e)}};for(const[t,n]of Object.entries(_))try{v.communicationLayer.on(t,n)}catch(e){}}function Lr(e){return l(this,arguments,void 0,(function*({options:e,instance:t}){const{state:n}=t;return Yn.RemoteCommunication(`[RemoteCommunication: disconnect()] channel=${n.channelId}`,e),new Promise(((r,i)=>{var s,a,c,d,l,u;(null==e?void 0:e.terminate)?(t.state.ready&&Xn({id:null!==(s=t.state.channelId)&&void 0!==s?s:"",event:lr.TERMINATED},t.state.communicationServerUrl).catch((e=>{})),n.ready=!1,n.paused=!1,null===(a=n.storageManager)||void 0===a||a.terminate(null!==(c=n.channelId)&&void 0!==c?c:""),t.state.terminated=!0,e.sendMessage?(null===(d=n.communicationLayer)||void 0===d?void 0:d.getKeyInfo().keysExchanged)&&t.state.communicationLayer&&vr(t.state.communicationLayer,{type:exports.MessageType.TERMINATE}).then((()=>{r(!0)})).catch((e=>{i(e)})):r(!0),n.authorized=!1,n.relayPersistence=!1,n.channelId=o.v4(),e.channelId=n.channelId,n.channelConfig=void 0,n.originatorConnectStarted=!1,null===(l=n.communicationLayer)||void 0===l||l.disconnect(e),t.setConnectionStatus(exports.ConnectionStatus.TERMINATED)):(null===(u=n.communicationLayer)||void 0===u||u.disconnect(e),t.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED),r(!0))}))}))}exports.CommunicationLayerPreference=void 0,exports.PlatformType=void 0,(exports.CommunicationLayerPreference||(exports.CommunicationLayerPreference={})).SOCKET="socket",function(e){e.NonBrowser="nodejs",e.MetaMaskMobileWebview="in-app-browser",e.DesktopWeb="web-desktop",e.MobileWeb="web-mobile",e.ReactNative="react-native"}(exports.PlatformType||(exports.PlatformType={}));class Br extends i.EventEmitter2{constructor(e){super(),this.state={ready:!1,authorized:!1,isOriginator:!1,terminated:!1,protocolVersion:1,paused:!1,deeplinkProtocolAvailable:!1,platformType:"metamask-mobile",analytics:!1,reconnection:!1,originatorInfoSent:!1,communicationServerUrl:nr,context:"",persist:!1,clientsConnected:!1,sessionDuration:ir,originatorConnectStarted:!1,debug:!1,_connectionStatus:exports.ConnectionStatus.DISCONNECTED},this._options=e;const{platformType:n,communicationLayerPreference:r,otherPublicKey:i,reconnect:o,walletInfo:s,dappMetadata:a,protocolVersion:c,transports:d,context:l,relayPersistence:u,ecies:h,analytics:f=!1,storage:p,sdkVersion:m,communicationServerUrl:g=nr,logging:v,autoConnect:y={timeout:or}}=e;this.state.otherPublicKey=i,this.state.dappMetadata=a,this.state.walletInfo=s,this.state.transports=d,this.state.platformType=n,this.state.analytics=f,this.state.protocolVersion=null!=c?c:1,this.state.isOriginator=!i,this.state.relayPersistence=u,this.state.communicationServerUrl=g,this.state.context=l,this.state.terminated=!1,this.state.sdkVersion=m,this.setMaxListeners(50),this.setConnectionStatus(exports.ConnectionStatus.DISCONNECTED),(null==p?void 0:p.duration)&&(this.state.sessionDuration=ir),this.state.storageOptions=p,this.state.autoConnectOptions=y,this.state.debug=!0===(null==v?void 0:v.remoteLayer),!0===(null==v?void 0:v.remoteLayer)&&t.enable("RemoteCommunication:Layer"),!0===(null==v?void 0:v.serviceLayer)&&t.enable("SocketService:Layer"),!0===(null==v?void 0:v.eciesLayer)&&t.enable("ECIES:Layer"),!0===(null==v?void 0:v.keyExchangeLayer)&&t.enable("KeyExchange:Layer"),this.state.logging=v,(null==p?void 0:p.storageManager)&&(this.state.storageManager=p.storageManager),Yn.RemoteCommunication(`[RemoteCommunication: constructor()] protocolVersion=${c} relayPersistence=${u} isOriginator=${this.state.isOriginator} communicationLayerPreference=${r} otherPublicKey=${i} reconnect=${o}`),this.state.isOriginator||Dr({communicationLayerPreference:r,otherPublicKey:i,reconnect:o,ecies:h,communicationServerUrl:g,instance:this}),this.emitServiceStatusEvent({context:"constructor"})}initFromDappStorage(){return l(this,void 0,void 0,(function*(){var e;if(this.state.storageManager){const t=yield this.state.storageManager.getPersistedChannelConfig({});t&&(this.state.channelConfig=t,this.state.channelId=t.channelId,this.state.deeplinkProtocolAvailable=null!==(e=t.deeplinkProtocolAvailable)&&void 0!==e&&e,t.relayPersistence&&(this.state.authorized=!0,this.state.ready=!0,this.setConnectionStatus(exports.ConnectionStatus.LINKED),yield this.connectToChannel({channelId:t.channelId})))}Dr({communicationLayerPreference:exports.CommunicationLayerPreference.SOCKET,otherPublicKey:this.state.otherPublicKey,reconnect:this._options.reconnect,ecies:this._options.ecies,communicationServerUrl:this.state.communicationServerUrl,instance:this})}))}originatorSessionConnect(){return l(this,void 0,void 0,(function*(){return yield function(e){return l(this,void 0,void 0,(function*(){var t;const{state:n}=e;if(!n.storageManager)return void Yn.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] no storage manager defined - skip");const r=yield n.storageManager.getPersistedChannelConfig({});if(Yn.RemoteCommunication(`[RemoteCommunication: originatorSessionConnect()] autoStarted=${n.originatorConnectStarted} channelConfig`,r),null===(t=n.communicationLayer)||void 0===t?void 0:t.isConnected())return Yn.RemoteCommunication("[RemoteCommunication: originatorSessionConnect()] socket already connected - skip"),r;if(r){if(r.validUntil>Date.now())return n.channelConfig=r,n.originatorConnectStarted=!0,n.channelId=null==r?void 0:r.channelId,n.reconnection=!0,r;Yn.RemoteCommunication("[RemoteCommunication: autoConnect()] Session has expired")}n.originatorConnectStarted=!1}))}(this)}))}generateChannelIdConnect(){return l(this,void 0,void 0,(function*(){return function(e){return l(this,void 0,void 0,(function*(){var t,n,r,i,o,s;if(!e.communicationLayer)throw new Error("communication layer not initialized");if(e.ready)throw new Error("Channel already connected");if(e.channelId&&(null===(t=e.communicationLayer)||void 0===t?void 0:t.isConnected()))return e.channelConfig=Object.assign(Object.assign({},e.channelConfig),{channelId:e.channelId,validUntil:Date.now()+e.sessionDuration}),null===(n=e.storageManager)||void 0===n||n.persistChannelConfig(e.channelConfig),{channelId:e.channelId,privKey:null===(i=null===(r=e.communicationLayer)||void 0===r?void 0:r.getKeyInfo())||void 0===i?void 0:i.ecies.private,pubKey:null===(s=null===(o=e.communicationLayer)||void 0===o?void 0:o.getKeyInfo())||void 0===s?void 0:s.ecies.public};Yn.RemoteCommunication("[RemoteCommunication: generateChannelId()]");const a=yield e.communicationLayer.createChannel();Yn.RemoteCommunication("[RemoteCommunication: generateChannelId()] channel created",a);const c=Object.assign(Object.assign({},e.channelConfig),{channelId:a.channelId,localKey:a.privKey,validUntil:Date.now()+e.sessionDuration});return e.channelId=a.channelId,e.channelConfig=c,{channelId:e.channelId,pubKey:a.pubKey,privKey:a.privKey}}))}(this.state)}))}clean(){return ar(this.state)}connectToChannel({channelId:e,withKeyExchange:t,authorized:n}){return function(e){return l(this,arguments,void 0,(function*({channelId:e,withKeyExchange:t,authorized:n,state:r}){var i,s,a;if(!o.validate(e))throw Yn.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(Yn.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} channelId=${e} withKeyExchange=${t}`),null===(i=r.communicationLayer)||void 0===i?void 0:i.isConnected())return void Yn.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${r.context} already connected - interrupt connection.`);r.channelId=e,yield null===(s=r.communicationLayer)||void 0===s?void 0:s.connectToChannel({channelId:e,authorized:n,withKeyExchange:t});const c=Object.assign(Object.assign({},r.channelConfig),{channelId:e,validUntil:Date.now()+r.sessionDuration});r.channelConfig=c,null===(a=r.storageManager)||void 0===a||a.persistChannelConfig(c)}))}({channelId:e,authorized:n,withKeyExchange:t,state:this.state})}sendMessage(e){return function(e,t){return l(this,void 0,void 0,(function*(){var n,r;const{state:i}=e;Yn.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context} paused=${i.paused} ready=${i.ready} relayPersistence=${i.relayPersistence} authorized=${i.authorized} socket=${null===(n=i.communicationLayer)||void 0===n?void 0:n.isConnected()} clientsConnected=${i.clientsConnected} status=${i._connectionStatus}`,t),i.relayPersistence||i.ready&&(null===(r=i.communicationLayer)||void 0===r?void 0:r.isConnected())&&i.clientsConnected||(Yn.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context}  SKIP message waiting for MM mobile readiness.`),yield new Promise((t=>{e.once(exports.EventType.CLIENTS_READY,t)})),Yn.RemoteCommunication(`[RemoteCommunication: sendMessage()] context=${i.context}  AFTER SKIP / READY -- sending pending message`));try{const n=yield function(e,t){return l(this,void 0,void 0,(function*(){return new Promise((n=>{var r;const{state:i}=e;Yn.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context} ready=${i.ready} authorized=${i.authorized} method=${t.method}`),!i.isOriginator||i.authorized||i.relayPersistence?null===(r=i.communicationLayer)||void 0===r||r.sendMessage(t).then((e=>{n(e)})).catch((e=>{n(!1)})):e.once(exports.EventType.AUTHORIZED,(()=>{var e;Yn.RemoteCommunication(`[RemoteCommunication: handleAuthorization()] context=${i.context}  AFTER SKIP / AUTHORIZED -- sending pending message`),null===(e=i.communicationLayer)||void 0===e||e.sendMessage(t).then((e=>{n(e)})).catch((e=>{n(!1)}))}))}))}))}(e,t);return n}catch(e){throw e}}))}(this,e)}testStorage(){return l(this,void 0,void 0,(function*(){return function(e){return l(this,void 0,void 0,(function*(){var t;const n=yield null===(t=e.storageManager)||void 0===t?void 0:t.getPersistedChannelConfig();Yn.RemoteCommunication("[RemoteCommunication: testStorage()] res",n)}))}(this.state)}))}hasDeeplinkProtocol(){return this.state.deeplinkProtocolAvailable}getChannelConfig(){return this.state.channelConfig}isReady(){return this.state.ready}isConnected(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.isConnected()}isAuthorized(){return this.state.authorized}isPaused(){return this.state.paused}getCommunicationLayer(){return this.state.communicationLayer}ping(){return l(this,void 0,void 0,(function*(){var e;Yn.RemoteCommunication(`[RemoteCommunication: ping()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.ping()}))}testLogger(){Yn.RemoteCommunication(`testLogger() channel=${this.state.channelId}`),Yn.SocketService(`testLogger() channel=${this.state.channelId}`),Yn.Ecies(`testLogger() channel=${this.state.channelId}`),Yn.KeyExchange(`testLogger() channel=${this.state.channelId}`)}keyCheck(){var e;Yn.RemoteCommunication(`[RemoteCommunication: keyCheck()] channel=${this.state.channelId}`),null===(e=this.state.communicationLayer)||void 0===e||e.keyCheck()}setConnectionStatus(e){this.state._connectionStatus!==e&&(this.state._connectionStatus=e,this.emit(exports.EventType.CONNECTION_STATUS,e),this.emitServiceStatusEvent({context:"setConnectionStatus"}))}emitServiceStatusEvent(e={}){this.emit(exports.EventType.SERVICE_STATUS,this.getServiceStatus())}getConnectionStatus(){return this.state._connectionStatus}getServiceStatus(){return{originatorInfo:this.state.originatorInfo,keyInfo:this.getKeyInfo(),connectionStatus:this.state._connectionStatus,channelConfig:this.state.channelConfig,channelId:this.state.channelId}}getKeyInfo(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getKeyInfo()}resetKeys(){var e;null===(e=this.state.communicationLayer)||void 0===e||e.resetKeys()}setOtherPublicKey(e){var t;const n=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange();if(!n)throw new Error("KeyExchange is not initialized.");n.getOtherPublicKey()!==e&&n.setOtherPublicKey(e)}pause(){return l(this,void 0,void 0,(function*(){var e;Yn.RemoteCommunication(`[RemoteCommunication: pause()] channel=${this.state.channelId}`),yield null===(e=this.state.communicationLayer)||void 0===e?void 0:e.pause(),this.setConnectionStatus(exports.ConnectionStatus.PAUSED)}))}getVersion(){return tr.version}hasRelayPersistence(){var e;return null!==(e=this.state.relayPersistence)&&void 0!==e&&e}resume(){return l(this,void 0,void 0,(function*(){return function(e){return l(this,void 0,void 0,(function*(){var t;const{state:n}=e;Yn.RemoteCommunication(`[RemoteCommunication: resume()] channel=${n.channelId}`),yield null===(t=n.communicationLayer)||void 0===t?void 0:t.resume(),e.setConnectionStatus(exports.ConnectionStatus.LINKED)}))}(this)}))}encrypt(e){var t,n,r;const i=null===(t=this.state.communicationLayer)||void 0===t?void 0:t.getKeyExchange(),o=null==i?void 0:i.getOtherPublicKey();if(!o)throw new Error("KeyExchange not completed");return null===(r=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===r?void 0:r.encrypt(e,o)}decrypt(e){var t,n,r;if(!(null===(t=this.state.communicationLayer)||void 0===t?void 0:t.state.eciesInstance))throw new Error("ECIES instance is not initialized");return null===(r=null===(n=this.state.communicationLayer)||void 0===n?void 0:n.state.eciesInstance)||void 0===r?void 0:r.decrypt(e)}getChannelId(){return this.state.channelId}getRPCMethodTracker(){var e;return null===(e=this.state.communicationLayer)||void 0===e?void 0:e.getRPCMethodTracker()}reject({channelId:e}){return function(e){return l(this,arguments,void 0,(function*({channelId:e,state:t}){var n,r,i;if(!o.validate(e))throw Yn.RemoteCommunication(`[RemoteCommunication: connectToChannel()] context=${t.context} invalid channel channelId=${e}`),new Error(`Invalid channel ${e}`);if(t.isOriginator)return void Yn.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} isOriginator=${t.isOriginator} channelId=${e}`);const{socket:s}=null!==(r=null===(n=t.communicationLayer)||void 0===n?void 0:n.state)&&void 0!==r?r:{};(null==s?void 0:s.connected)||(Yn.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket already connected`),null==s||s.connect()),Xn(Object.assign(Object.assign({id:e,event:lr.REJECTED},t.originatorInfo),{sdkVersion:t.sdkVersion,commLayerVersion:tr.version,walletVersion:null===(i=t.walletInfo)||void 0===i?void 0:i.version}),t.communicationServerUrl).catch((e=>{})),yield new Promise(((n,r)=>{null==s||s.emit(exports.EventType.REJECTED,{channelId:e},((e,i)=>{Yn.RemoteCommunication(`[RemoteCommunication: reject()] context=${t.context} socket=${null==s?void 0:s.id}`,{error:e,response:i}),e?r(e):n(i)}))}))}))}({channelId:e,state:this.state})}disconnect(e){return l(this,void 0,void 0,(function*(){return Lr({options:e,instance:this})}))}}!function(e){e.RENEW="renew",e.LINK="link"}(Cr||(Cr={}));var Kr="ERC721",jr="ERC1155",$r={errors:{disconnected:()=>"MetaMask: Disconnected from chain. Attempting to connect.",permanentlyDisconnected:()=>"MetaMask: Disconnected from MetaMask background. Page reload required.",sendSiteMetadata:()=>"MetaMask: Failed to send site metadata. This is an internal error, please report this bug.",unsupportedSync:e=>`MetaMask: The MetaMask Ethereum provider does not support synchronous methods like ${e} without a callback parameter.`,invalidDuplexStream:()=>"Must provide a Node.js-style duplex stream.",invalidNetworkParams:()=>"MetaMask: Received invalid network parameters. Please report this bug.",invalidRequestArgs:()=>"Expected a single, non-array, object argument.",invalidRequestMethod:()=>"'args.method' must be a non-empty string.",invalidRequestParams:()=>"'args.params' must be an object or array if provided.",invalidLoggerObject:()=>"'args.logger' must be an object if provided.",invalidLoggerMethod:e=>`'args.logger' must include required method '${e}'.`},info:{connected:e=>`MetaMask: Connected to chain with ID "${e}".`},warnings:{chainIdDeprecation:"MetaMask: 'ethereum.chainId' is deprecated and may be removed in the future. Please use the 'eth_chainId' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",networkVersionDeprecation:"MetaMask: 'ethereum.networkVersion' is deprecated and may be removed in the future. Please use the 'net_version' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",selectedAddressDeprecation:"MetaMask: 'ethereum.selectedAddress' is deprecated and may be removed in the future. Please use the 'eth_accounts' RPC method instead.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/discussions/23",enableDeprecation:"MetaMask: 'ethereum.enable()' is deprecated and may be removed in the future. Please use the 'eth_requestAccounts' RPC method instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1102",sendDeprecation:"MetaMask: 'ethereum.send(...)' is deprecated and may be removed in the future. Please use 'ethereum.sendAsync(...)' or 'ethereum.request(...)' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193",events:{close:"MetaMask: The event 'close' is deprecated and may be removed in the future. Please use 'disconnect' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#disconnect",data:"MetaMask: The event 'data' is deprecated and will be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message",networkChanged:"MetaMask: The event 'networkChanged' is deprecated and may be removed in the future. Use 'chainChanged' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#chainchanged",notification:"MetaMask: The event 'notification' is deprecated and may be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message"},rpc:{ethDecryptDeprecation:"MetaMask: The RPC method 'eth_decrypt' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",ethGetEncryptionPublicKeyDeprecation:"MetaMask: The RPC method 'eth_getEncryptionPublicKey' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",walletWatchAssetNFTExperimental:"MetaMask: The RPC method 'wallet_watchAsset' is experimental for ERC721/ERC1155 assets and may change in the future.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-1.md and https://github.com/MetaMask/metamask-improvement-proposals/blob/main/PROCESS-GUIDE.md#proposal-lifecycle"},experimentalMethods:"MetaMask: 'ethereum._metamask' exposes non-standard, experimental methods. They may be removed or changed without warning."}};function Hr(e){const t={ethDecryptDeprecation:!1,ethGetEncryptionPublicKeyDeprecation:!1,walletWatchAssetNFTExperimental:!1};return(n,r,i)=>{t.ethDecryptDeprecation||"eth_decrypt"!==n.method?t.ethGetEncryptionPublicKeyDeprecation||"eth_getEncryptionPublicKey"!==n.method?!t.walletWatchAssetNFTExperimental&&"wallet_watchAsset"===n.method&&[Kr,jr].includes(n.params?.type||"")&&(e.warn($r.warnings.rpc.walletWatchAssetNFTExperimental),t.walletWatchAssetNFTExperimental=!0):(e.warn($r.warnings.rpc.ethGetEncryptionPublicKeyDeprecation),t.ethGetEncryptionPublicKeyDeprecation=!0):(e.warn($r.warnings.rpc.ethDecryptDeprecation),t.ethDecryptDeprecation=!0),i()}}var Ur=4294967295,Fr=Math.floor(Math.random()*Ur);function qr(){return(e,t,n,r)=>{const i=e.id,o=Fr=(Fr+1)%Ur;e.id=o,t.id=o,n((n=>{e.id=i,t.id=i,n()}))}}var zr=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},Wr=(e,t,n)=>(zr(e,t,"read from private field"),n?n.call(e):t.get(e)),Vr=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},Gr=(e,t,n,r)=>(zr(e,t,"write to private field"),t.set(e,n),n),Yr=(e,t,n)=>(zr(e,t,"access private method"),n),Zr={invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},Jr={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}};function Qr(e){return Boolean(e)&&"object"==typeof e&&!Array.isArray(e)}var Xr=(e,t)=>Object.hasOwnProperty.call(e,t);class ei extends TypeError{constructor(e,t){let n;const{message:r,explanation:i,...o}=e,{path:s}=e,a=0===s.length?r:`At path: ${s.join(".")} -- ${r}`;super(i??a),null!=i&&(this.cause=a),Object.assign(this,o),this.name=this.constructor.name,this.failures=()=>n??(n=[e,...t()])}}function ti(e){return"object"==typeof e&&null!=e}function ni(e){return"symbol"==typeof e?e.toString():"string"==typeof e?JSON.stringify(e):`${e}`}function ri(e,t,n,r){if(!0===e)return;!1===e?e={}:"string"==typeof e&&(e={message:e});const{path:i,branch:o}=t,{type:s}=n,{refinement:a,message:c=`Expected a value of type \`${s}\`${a?` with refinement \`${a}\``:""}, but received: \`${ni(r)}\``}=e;return{value:r,type:s,refinement:a,key:i[i.length-1],path:i,branch:o,...e,message:c}}function*ii(e,t,n,r){(function(e){return ti(e)&&"function"==typeof e[Symbol.iterator]})(e)||(e=[e]);for(const i of e){const e=ri(i,t,n,r);e&&(yield e)}}function*oi(e,t,n={}){const{path:r=[],branch:i=[e],coerce:o=!1,mask:s=!1}=n,a={path:r,branch:i};if(o&&(e=t.coercer(e,a),s&&"type"!==t.type&&ti(t.schema)&&ti(e)&&!Array.isArray(e)))for(const n in e)void 0===t.schema[n]&&delete e[n];let c="valid";for(const r of t.validator(e,a))r.explanation=n.message,c="not_valid",yield[r,void 0];for(let[d,l,u]of t.entries(e,a)){const t=oi(l,u,{path:void 0===d?r:[...r,d],branch:void 0===d?i:[...i,l],coerce:o,mask:s,message:n.message});for(const n of t)n[0]?(c=null!=n[0].refinement?"not_refined":"not_valid",yield[n[0],void 0]):o&&(l=n[1],void 0===d?e=l:e instanceof Map?e.set(d,l):e instanceof Set?e.add(l):ti(e)&&(void 0!==l||d in e)&&(e[d]=l))}if("not_valid"!==c)for(const r of t.refiner(e,a))r.explanation=n.message,c="not_refined",yield[r,void 0];"valid"===c&&(yield[void 0,e])}class si{constructor(e){const{type:t,schema:n,validator:r,refiner:i,coercer:o=(e=>e),entries:s=function*(){}}=e;this.type=t,this.schema=n,this.entries=s,this.coercer=o,this.validator=r?(e,t)=>ii(r(e,t),t,this,e):()=>[],this.refiner=i?(e,t)=>ii(i(e,t),t,this,e):()=>[]}assert(e,t){return ai(e,this,t)}create(e,t){return ci(e,this,t)}is(e){return di(e,this)}mask(e,t){return function(e,t,n){const r=li(e,t,{coerce:!0,mask:!0,message:n});if(r[0])throw r[0];return r[1]}(e,this,t)}validate(e,t={}){return li(e,this,t)}}function ai(e,t,n){const r=li(e,t,{message:n});if(r[0])throw r[0]}function ci(e,t,n){const r=li(e,t,{coerce:!0,message:n});if(r[0])throw r[0];return r[1]}function di(e,t){return!li(e,t)[0]}function li(e,t,n={}){const r=oi(e,t,n),i=function(e){const{done:t,value:n}=e.next();return t?void 0:n}(r);if(i[0]){const e=new ei(i[0],(function*(){for(const e of r)e[0]&&(yield e[0])}));return[e,void 0]}return[void 0,i[1]]}function ui(e,t){return new si({type:e,schema:null,validator:t})}function hi(e){let t;return new si({type:"lazy",schema:null,*entries(n,r){t??(t=e()),yield*t.entries(n,r)},validator:(n,r)=>(t??(t=e()),t.validator(n,r)),coercer:(n,r)=>(t??(t=e()),t.coercer(n,r)),refiner:(n,r)=>(t??(t=e()),t.refiner(n,r))})}function fi(e){return new si({type:"array",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[n,r]of t.entries())yield[n,r,e]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||`Expected an array value, but received: ${ni(e)}`})}function pi(e){const t=ni(e),n=typeof e;return new si({type:"literal",schema:"string"===n||"number"===n||"boolean"===n?e:null,validator:n=>n===e||`Expected the literal \`${t}\`, but received: ${ni(n)}`})}function mi(){return ui("number",(e=>"number"==typeof e&&!isNaN(e)||`Expected a number, but received: ${ni(e)}`))}function gi(e){const t=e?Object.keys(e):[],n=ui("never",(()=>!1));return new si({type:"object",schema:e||null,*entries(r){if(e&&ti(r)){const i=new Set(Object.keys(r));for(const n of t)i.delete(n),yield[n,r[n],e[n]];for(const e of i)yield[e,r[e],n]}},validator:e=>ti(e)||`Expected an object, but received: ${ni(e)}`,coercer:e=>ti(e)?{...e}:e})}function vi(e){return new si({...e,validator:(t,n)=>void 0===t||e.validator(t,n),refiner:(t,n)=>void 0===t||e.refiner(t,n)})}function yi(e,t){return new si({type:"record",schema:null,*entries(n){if(ti(n))for(const r in n){const i=n[r];yield[r,r,e],yield[r,i,t]}},validator:e=>ti(e)||`Expected an object, but received: ${ni(e)}`})}function Ei(){return ui("string",(e=>"string"==typeof e||`Expected a string, but received: ${ni(e)}`))}function bi(e){const t=e.map((e=>e.type)).join(" | ");return new si({type:"union",schema:null,coercer(t){for(const n of e){const[e,r]=n.validate(t,{coerce:!0});if(!e)return r}return t},validator(n,r){const i=[];for(const t of e){const[...e]=oi(n,t,r),[o]=e;if(!o[0])return[];for(const[t]of e)t&&i.push(t)}return[`Expected the value to satisfy a union of \`${t}\`, but received: ${ni(n)}`,...i]}})}function wi(e){return function(e){return function(e){return"object"==typeof e&&null!==e&&"message"in e}(e)&&"string"==typeof e.message?e.message:null==e?"":String(e)}(e).replace(/\.$/u,"")}function Si(e,t){return n=e,Boolean("string"==typeof n?.prototype?.constructor?.name)?new e({message:t}):e({message:t});var n}var _i=class extends Error{constructor(e){super(e.message),this.code="ERR_ASSERTION"}};var Ci=e=>gi(e);function xi({path:e,branch:t}){const n=e[e.length-1];return Xr(t[t.length-2],n)}function ki(e){return new si({...e,type:`optional ${e.type}`,validator:(t,n)=>!xi(n)||e.validator(t,n),refiner:(t,n)=>!xi(n)||e.refiner(t,n)})}var Mi,Ai,Ti,Ii=bi([pi(null),ui("boolean",(e=>"boolean"==typeof e)),ui("finite number",(e=>di(e,mi())&&Number.isFinite(e))),Ei(),fi(hi((()=>Ii))),yi(Ei(),hi((()=>Ii)))]),Pi=(Mi=Ii,Ai=ui("any",(()=>!0)),Ti=e=>(function(e,t,n="Assertion failed",r=_i){try{ai(e,t)}catch(e){throw Si(r,`${n}: ${wi(e)}.`)}}(e,Ii),JSON.parse(JSON.stringify(e,((e,t)=>{if("__proto__"!==e&&"constructor"!==e)return t})))),new si({...Mi,coercer:(e,t)=>di(e,Ai)?Mi.coercer(Ti(e,t),t):Mi.coercer(e,t)}));function Ri(e){try{return function(e){ci(e,Pi)}(e),!0}catch{return!1}}var Oi=pi("2.0"),Ni=function(e){return new si({...e,validator:(t,n)=>null===t||e.validator(t,n),refiner:(t,n)=>null===t||e.refiner(t,n)})}(bi([mi(),Ei()])),Di=Ci({code:ui("integer",(e=>"number"==typeof e&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${ni(e)}`)),message:Ei(),data:ki(Pi),stack:ki(Ei())}),Li=bi([yi(Ei(),Pi),fi(Pi)]),Bi=Ci({id:Ni,jsonrpc:Oi,method:Ei(),params:ki(Li)}),Ki=Ci({jsonrpc:Oi,method:Ei(),params:ki(Li)});function ji(e){return di(e,Bi)}function $i(e){return di(e,Di)}gi({id:Ni,jsonrpc:Oi,result:vi(ui("unknown",(()=>!0))),error:vi(Di)}),bi([Ci({id:Ni,jsonrpc:Oi,result:Pi}),Ci({id:Ni,jsonrpc:Oi,error:Di})]);var Hi=Zr.internal,Ui="Unspecified error message. This is a bug, please report it.",Fi={code:Hi,message:zi(Hi)},qi="Unspecified server error.";function zi(e,t=Ui){if(function(e){return Number.isInteger(e)}(e)){const t=e.toString();if(Xr(Jr,t))return Jr[t].message;if(function(e){return e>=-32099&&e<=-32e3}(e))return qi}return t}function Wi(e,{fallbackError:t=Fi,shouldIncludeStack:n=!0}={}){if(!$i(t))throw new Error("Must provide fallback error with integer number code and string message.");const r=function(e,t){if(e&&"object"==typeof e&&"serialize"in e&&"function"==typeof e.serialize)return e.serialize();if($i(e))return e;const n=Vi(e),r={...t,data:{cause:n}};return r}(e,t);return n||delete r.stack,r}function Vi(e){return Array.isArray(e)?e.map((e=>Ri(e)?e:Qr(e)?Gi(e):null)):Qr(e)?Gi(e):Ri(e)?e:null}function Gi(e){return Object.getOwnPropertyNames(e).reduce(((t,n)=>{const r=e[n];return Ri(r)&&(t[n]=r),t}),{})}var Yi=to;to.default=to,to.stable=oo,to.stableStringify=oo;var Zi="[...]",Ji="[Circular]",Qi=[],Xi=[];function eo(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function to(e,t,n,r){var i;void 0===r&&(r=eo()),ro(e,"",0,[],void 0,0,r);try{i=0===Xi.length?JSON.stringify(e,t,n):JSON.stringify(e,ao(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Qi.length;){var o=Qi.pop();4===o.length?Object.defineProperty(o[0],o[1],o[3]):o[0][o[1]]=o[2]}}return i}function no(e,t,n,r){var i=Object.getOwnPropertyDescriptor(r,n);void 0!==i.get?i.configurable?(Object.defineProperty(r,n,{value:e}),Qi.push([r,n,t,i])):Xi.push([t,n,e]):(r[n]=e,Qi.push([r,n,t]))}function ro(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void no(Ji,e,t,i);if(void 0!==s.depthLimit&&o>s.depthLimit)return void no(Zi,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void no(Zi,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)ro(e[a],a,a,r,e,o,s);else{var c=Object.keys(e);for(a=0;a<c.length;a++){var d=c[a];ro(e[d],d,a,r,e,o,s)}}r.pop()}}function io(e,t){return e<t?-1:e>t?1:0}function oo(e,t,n,r){void 0===r&&(r=eo());var i,o=so(e,"",0,[],void 0,0,r)||e;try{i=0===Xi.length?JSON.stringify(o,t,n):JSON.stringify(o,ao(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Qi.length;){var s=Qi.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return i}function so(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void no(Ji,e,t,i);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==s.depthLimit&&o>s.depthLimit)return void no(Zi,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void no(Zi,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)so(e[a],a,a,r,e,o,s);else{var c={},d=Object.keys(e).sort(io);for(a=0;a<d.length;a++){var l=d[a];so(e[l],l,a,r,e,o,s),c[l]=e[l]}if(void 0===i)return c;Qi.push([i,t,e]),i[t]=c}r.pop()}}function ao(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(Xi.length>0)for(var r=0;r<Xi.length;r++){var i=Xi[r];if(i[1]===t&&i[0]===n){n=i[2],Xi.splice(r,1);break}}return e.call(this,t,n)}}var co=h(Yi),lo=class extends Error{constructor(e,t,n){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a non-empty string.');super(t),this.code=e,void 0!==n&&(this.data=n)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data,function(e){if("object"!=typeof e||null===e)return!1;try{let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}catch(e){return!1}}(this.data)&&(e.data.cause=Vi(this.data.cause))),this.stack&&(e.stack=this.stack),e}toString(){return co(this.serialize(),uo,2)}};function uo(e,t){if("[Circular]"!==t)return t}var ho=e=>fo(Zr.invalidRequest,e);function fo(e,t){const[n,r]=function(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:n}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t??void 0,n]}}return[]}(t);return new lo(e,n??zi(e),r)}var po={};Object.defineProperty(po,"__esModule",{value:!0});const mo=a;function go(e,t,n){try{Reflect.apply(e,t,n)}catch(e){setTimeout((()=>{throw e}))}}let vo=class extends mo.EventEmitter{emit(e,...t){let n="error"===e;const r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const n=new Error("Unhandled error."+(e?` (${e.message})`:""));throw n.context=e,n}const i=r[e];if(void 0===i)return!1;if("function"==typeof i)go(i,this,t);else{const e=i.length,n=function(e){const t=e.length,n=new Array(t);for(let r=0;r<t;r+=1)n[r]=e[r];return n}(i);for(let r=0;r<e;r+=1)go(n[r],this,t)}return!0}};var yo,Eo,bo,wo,So,_o,Co,xo,ko,Mo,Ao,To,Io,Po,Ro,Oo,No,Do,Lo,Bo=po.default=vo,Ko=class e extends Bo{constructor({notificationHandler:e}={}){super(),Vr(this,wo),Vr(this,_o),Vr(this,xo),Vr(this,yo,!1),Vr(this,Eo,void 0),Vr(this,bo,void 0),Gr(this,Eo,[]),Gr(this,bo,e)}destroy(){Wr(this,Eo).forEach((e=>{"destroy"in e&&"function"==typeof e.destroy&&e.destroy()})),Gr(this,Eo,[]),Gr(this,yo,!0)}push(e){Yr(this,wo,So).call(this),Wr(this,Eo).push(e)}handle(e,t){if(Yr(this,wo,So).call(this),t&&"function"!=typeof t)throw new Error('"callback" must be a function if provided.');return Array.isArray(e)?t?Yr(this,_o,Co).call(this,e,t):Yr(this,_o,Co).call(this,e):t?Yr(this,xo,ko).call(this,e,t):this._promiseHandle(e)}asMiddleware(){return Yr(this,wo,So).call(this),async(t,n,r,i)=>{var o,s;try{const[a,c,d]=await Yr(o=e,To,Io).call(o,t,n,Wr(this,Eo));return c?(await Yr(s=e,Oo,No).call(s,d),i(a)):r((async t=>{var n;try{await Yr(n=e,Oo,No).call(n,d)}catch(e){return t(e)}return t()}))}catch(e){return i(e)}}}async _promiseHandle(e){return new Promise(((t,n)=>{Yr(this,xo,ko).call(this,e,((e,r)=>{e&&void 0===r?n(e):t(r)})).catch(n)}))}};yo=new WeakMap,Eo=new WeakMap,bo=new WeakMap,wo=new WeakSet,So=function(){if(Wr(this,yo))throw new Error("This engine is destroyed and can no longer be used.")},_o=new WeakSet,Co=async function(e,t){try{if(0===e.length){const e=[{id:null,jsonrpc:"2.0",error:new lo(Zr.invalidRequest,"Request batch must contain plain objects. Received an empty array")}];return t?t(null,e):e}const n=(await Promise.all(e.map(this._promiseHandle.bind(this)))).filter((e=>void 0!==e));return t?t(null,n):n}catch(e){if(t)return t(e);throw e}},xo=new WeakSet,ko=async function(e,t){var n;if(!e||Array.isArray(e)||"object"!=typeof e){const n=new lo(Zr.invalidRequest,"Requests must be plain objects. Received: "+typeof e,{request:e});return t(n,{id:null,jsonrpc:"2.0",error:n})}if("string"!=typeof e.method){const n=new lo(Zr.invalidRequest,"Must specify a string method. Received: "+typeof e.method,{request:e});return Wr(this,bo)&&!ji(e)?t(null):t(n,{id:e.id??null,jsonrpc:"2.0",error:n})}if(Wr(this,bo)&&di(e,Ki)&&!ji(e)){try{await Wr(this,bo).call(this,e)}catch(e){return t(e)}return t(null)}let r=null;const i={...e},o={id:i.id,jsonrpc:i.jsonrpc};try{await Yr(n=Ko,Mo,Ao).call(n,i,o,Wr(this,Eo))}catch(e){r=e}return r&&(delete o.result,o.error||(o.error=Wi(r))),t(r,o)},Mo=new WeakSet,Ao=async function(e,t,n){var r,i,o;const[s,a,c]=await Yr(r=Ko,To,Io).call(r,e,t,n);if(Yr(i=Ko,Do,Lo).call(i,e,t,a),await Yr(o=Ko,Oo,No).call(o,c),s)throw s},To=new WeakSet,Io=async function(e,t,n){var r;const i=[];let o=null,s=!1;for(const a of n)if([o,s]=await Yr(r=Ko,Po,Ro).call(r,e,t,a,i),s)break;return[o,s,i.reverse()]},Po=new WeakSet,Ro=async function(e,t,n,r){return new Promise((i=>{const o=e=>{const n=e||t.error;n&&(t.error=Wi(n)),i([n,!0])},s=n=>{t.error?o(t.error):(n&&("function"!=typeof n&&o(new lo(Zr.internal,`JsonRpcEngine: "next" return handlers must be functions. Received "${typeof n}" for request:\n${$o(e)}`,{request:e})),r.push(n)),i([null,!1]))};try{n(e,t,s,o)}catch(e){o(e)}}))},Oo=new WeakSet,No=async function(e){for(const t of e)await new Promise(((e,n)=>{t((t=>t?n(t):e()))}))},Do=new WeakSet,Lo=function(e,t,n){if(!Xr(t,"result")&&!Xr(t,"error"))throw new lo(Zr.internal,`JsonRpcEngine: Response has no error or result for request:\n${$o(e)}`,{request:e});if(!n)throw new lo(Zr.internal,`JsonRpcEngine: Nothing ended request:\n${$o(e)}`,{request:e})},Vr(Ko,Mo),Vr(Ko,To),Vr(Ko,Po),Vr(Ko,Oo),Vr(Ko,Do);var jo=Ko;function $o(e){return JSON.stringify(e,null,2)}var Ho=Object.freeze(["eth_subscription"]),Uo=(e=console)=>{return[qr(),(t=e,(e,n,r)=>{"string"==typeof e.method&&e.method||(n.error=ho({message:"The request 'method' must be a non-empty string.",data:e})),r((e=>{const{error:r}=n;return r?(t.error(`MetaMask - RPC Error: ${r.message}`,r),e()):e()}))}),Hr(e)];var t};var Fo=(e,t,n=!0)=>(r,i)=>{r||i.error?t(r||i.error):!n||Array.isArray(i)?e(i):e(i.result)},qo=e=>Boolean(e)&&"string"==typeof e&&e.startsWith("0x"),zo=()=>{};async function Wo(e,t){try{const t=await async function(){return{name:Vo(window),icon:await Go(window)}}();e.handle({jsonrpc:"2.0",id:1,method:"metamask_sendDomainMetadata",params:t},zo)}catch(e){t.error({message:$r.errors.sendSiteMetadata(),originalError:e})}}function Vo(e){const{document:t}=e,n=t.querySelector('head > meta[property="og:site_name"]');if(n)return n.content;const r=t.querySelector('head > meta[name="title"]');return r?r.content:t.title&&t.title.length>0?t.title:window.location.hostname}async function Go(e){const{document:t}=e,n=t.querySelectorAll('head > link[rel~="icon"]');for(const e of Array.from(n))if(e&&await Yo(e.href))return e.href;return null}async function Yo(e){return new Promise(((t,n)=>{try{const n=document.createElement("img");n.onload=()=>t(!0),n.onerror=()=>t(!1),n.src=e}catch(e){n(e)}}))}var Zo=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},Jo=(e,t,n)=>(Zo(e,t,"read from private field"),n?n.call(e):t.get(e)),Qo=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},Xo=(e,t,n,r)=>(Zo(e,t,"write to private field"),t.set(e,n),n);function es(e,t,n){try{Reflect.apply(e,t,n)}catch(e){setTimeout((()=>{throw e}))}}class ts extends a.EventEmitter{emit(e,...t){let n="error"===e;const r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const n=new Error("Unhandled error."+(e?` (${e.message})`:""));throw n.context=e,n}const i=r[e];if(void 0===i)return!1;if("function"==typeof i)es(i,this,t);else{const e=i.length,n=function(e){const t=e.length,n=new Array(t);for(let r=0;r<t;r+=1)n[r]=e[r];return n}(i);for(let r=0;r<e;r+=1)es(n[r],this,t)}return!0}}var ns,rs,is=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,i,o;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var s=o[i];if(!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n},os=h(is),ss=class e extends ts{constructor({logger:t=console,maxEventListeners:n=100,rpcMiddleware:r=[]}={}){super(),Qo(this,ns,void 0),Qo(this,rs,void 0),this._log=t,this.setMaxListeners(n),this._state={...e._defaultState},Xo(this,rs,null),Xo(this,ns,null),this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleConnect=this._handleConnect.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._handleUnlockStateChanged=this._handleUnlockStateChanged.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this.request=this.request.bind(this);const i=new jo;r.forEach((e=>i.push(e))),this._rpcEngine=i}get chainId(){return Jo(this,ns)}get selectedAddress(){return Jo(this,rs)}isConnected(){return this._state.isConnected}async request(e){if(!e||"object"!=typeof e||Array.isArray(e))throw ho({message:$r.errors.invalidRequestArgs(),data:e});const{method:t,params:n}=e;if("string"!=typeof t||0===t.length)throw ho({message:$r.errors.invalidRequestMethod(),data:e});if(void 0!==n&&!Array.isArray(n)&&("object"!=typeof n||null===n))throw ho({message:$r.errors.invalidRequestParams(),data:e});const r=null==n?{method:t}:{method:t,params:n};return new Promise(((e,t)=>{this._rpcRequest(r,Fo(e,t))}))}_initializeState(e){if(this._state.initialized)throw new Error("Provider already initialized.");if(e){const{accounts:t,chainId:n,isUnlocked:r,networkVersion:i}=e;this._handleConnect(n),this._handleChainChanged({chainId:n,networkVersion:i}),this._handleUnlockStateChanged({accounts:t,isUnlocked:r}),this._handleAccountsChanged(t)}this._state.initialized=!0,this.emit("_initialized")}_rpcRequest(e,t){let n=t;return Array.isArray(e)||(e.jsonrpc||(e.jsonrpc="2.0"),"eth_accounts"!==e.method&&"eth_requestAccounts"!==e.method||(n=(n,r)=>{this._handleAccountsChanged(r.result??[],"eth_accounts"===e.method),t(n,r)})),this._rpcEngine.handle(e,n)}_handleConnect(e){this._state.isConnected||(this._state.isConnected=!0,this.emit("connect",{chainId:e}),this._log.debug($r.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){let n;this._state.isConnected=!1,e?(n=new lo(1013,t??$r.errors.disconnected()),this._log.debug(n)):(n=new lo(1011,t??$r.errors.permanentlyDisconnected()),this._log.error(n),Xo(this,ns,null),this._state.accounts=null,Xo(this,rs,null),this._state.isUnlocked=!1,this._state.isPermanentlyDisconnected=!0),this.emit("disconnect",n)}}_handleChainChanged({chainId:e}={}){qo(e)?(this._handleConnect(e),e!==Jo(this,ns)&&(Xo(this,ns,e),this._state.initialized&&this.emit("chainChanged",Jo(this,ns)))):this._log.error($r.errors.invalidNetworkParams(),{chainId:e})}_handleAccountsChanged(e,t=!1){let n=e;Array.isArray(e)||(this._log.error("MetaMask: Received invalid accounts parameter. Please report this bug.",e),n=[]);for(const t of e)if("string"!=typeof t){this._log.error("MetaMask: Received non-string account. Please report this bug.",e),n=[];break}if(!os(this._state.accounts,n)&&(t&&null!==this._state.accounts&&this._log.error("MetaMask: 'eth_accounts' unexpectedly updated accounts. Please report this bug.",n),this._state.accounts=n,Jo(this,rs)!==n[0]&&Xo(this,rs,n[0]||null),this._state.initialized)){const e=[...n];this.emit("accountsChanged",e)}}_handleUnlockStateChanged({accounts:e,isUnlocked:t}={}){"boolean"==typeof t?t!==this._state.isUnlocked&&(this._state.isUnlocked=t,this._handleAccountsChanged(e??[])):this._log.error("MetaMask: Received invalid isUnlocked parameter. Please report this bug.")}};ns=new WeakMap,rs=new WeakMap,ss._defaultState={accounts:null,isConnected:!1,isUnlocked:!1,initialized:!1,isPermanentlyDisconnected:!1};var as,cs,ds,ls,us,hs,fs=ss,ps={exports:{}};function ms(){return cs?as:(cs=1,as=c)}function gs(){if(ls)return ds;function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?e(Object(i),!0).forEach((function(e){r(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function r(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,o(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}ls=1;var s=n.Buffer,a=d.inspect,c=a&&a.custom||"inspect";return ds=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return i(e,[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n}},{key:"concat",value:function(e){if(0===this.length)return s.alloc(0);for(var t,n,r,i=s.allocUnsafe(e>>>0),o=this.head,a=0;o;)t=o.data,n=i,r=a,s.prototype.copy.call(t,n,r),a+=o.data.length,o=o.next;return i}},{key:"consume",value:function(e,t){var n;return e<this.head.data.length?(n=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):n=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),n}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,n=1,r=t.data;for(e-=r.length;t=t.next;){var i=t.data,o=e>i.length?i.length:e;if(o===i.length?r+=i:r+=i.slice(0,e),0===(e-=o)){o===i.length?(++n,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=i.slice(o));break}++n}return this.length-=n,r}},{key:"_getBuffer",value:function(e){var t=s.allocUnsafe(e),n=this.head,r=1;for(n.data.copy(t),e-=n.data.length;n=n.next;){var i=n.data,o=e>i.length?i.length:e;if(i.copy(t,t.length-e,0,o),0===(e-=o)){o===i.length?(++r,n.next?this.head=n.next:this.head=this.tail=null):(this.head=n,n.data=i.slice(o));break}++r}return this.length-=r,t}},{key:c,value:function(e,n){return a(this,t(t({},n),{},{depth:0,customInspect:!1}))}}]),e}(),ds}function vs(){if(hs)return us;function e(e,r){n(e,r),t(e)}function t(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function n(e,t){e.emit("error",t)}return hs=1,us={destroy:function(r,i){var o=this,s=this._readableState&&this._readableState.destroyed,a=this._writableState&&this._writableState.destroyed;return s||a?(i?i(r):r&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(n,this,r)):process.nextTick(n,this,r)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(r||null,(function(n){!i&&n?o._writableState?o._writableState.errorEmitted?process.nextTick(t,o):(o._writableState.errorEmitted=!0,process.nextTick(e,o,n)):process.nextTick(e,o,n):i?(process.nextTick(t,o),i(n)):process.nextTick(t,o)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var n=e._readableState,r=e._writableState;n&&n.autoDestroy||r&&r.autoDestroy?e.destroy(t):e.emit("error",t)}},us}var ys,Es,bs,ws={};function Ss(){if(ys)return ws;ys=1;const e={};function t(t,n,r){r||(r=Error);class i extends r{constructor(e,t,r){super(function(e,t,r){return"string"==typeof n?n:n(e,t,r)}(e,t,r))}}i.prototype.name=r.name,i.prototype.code=t,e[t]=i}function n(e,t){if(Array.isArray(e)){const n=e.length;return e=e.map((e=>String(e))),n>2?`one of ${t} ${e.slice(0,n-1).join(", ")}, or `+e[n-1]:2===n?`one of ${t} ${e[0]} or ${e[1]}`:`of ${t} ${e[0]}`}return`of ${t} ${String(e)}`}return t("ERR_INVALID_OPT_VALUE",(function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'}),TypeError),t("ERR_INVALID_ARG_TYPE",(function(e,t,r){let i;var o;let s;if("string"==typeof t&&(o="not ",t.substr(0,o.length)===o)?(i="must not be",t=t.replace(/^not /,"")):i="must be",function(e,t,n){return(void 0===n||n>e.length)&&(n=e.length),e.substring(n-t.length,n)===t}(e," argument"))s=`The ${e} ${i} ${n(t,"type")}`;else{const r=function(e,t,n){return"number"!=typeof n&&(n=0),!(n+t.length>e.length)&&-1!==e.indexOf(t,n)}(e,".")?"property":"argument";s=`The "${e}" ${r} ${i} ${n(t,"type")}`}return s+=". Received type "+typeof r,s}),TypeError),t("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),t("ERR_METHOD_NOT_IMPLEMENTED",(function(e){return"The "+e+" method is not implemented"})),t("ERR_STREAM_PREMATURE_CLOSE","Premature close"),t("ERR_STREAM_DESTROYED",(function(e){return"Cannot call "+e+" after a stream was destroyed"})),t("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),t("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),t("ERR_STREAM_WRITE_AFTER_END","write after end"),t("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),t("ERR_UNKNOWN_ENCODING",(function(e){return"Unknown encoding: "+e}),TypeError),t("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),ws.codes=e,ws}function _s(){if(bs)return Es;bs=1;var e=Ss().codes.ERR_INVALID_OPT_VALUE;return Es={getHighWaterMark:function(t,n,r,i){var o=function(e,t,n){return null!=e.highWaterMark?e.highWaterMark:t?e[n]:null}(n,i,r);if(null!=o){if(!isFinite(o)||Math.floor(o)!==o||o<0)throw new e(i?r:"highWaterMark",o);return Math.floor(o)}return t.objectMode?16:16384}}}var Cs,xs,ks,Ms,As,Ts,Is,Ps,Rs={exports:{}},Os={exports:{}};function Ns(){if(xs)return Rs.exports;xs=1;try{var e=require("util");if("function"!=typeof e.inherits)throw"";Rs.exports=e.inherits}catch(e){Rs.exports=(Cs||(Cs=1,"function"==typeof Object.create?Os.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Os.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}),Os.exports)}return Rs.exports}function Ds(){if(Ts)return As;function e(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,n){var r=e.entry;e.entry=null;for(;r;){var i=r.callback;t.pendingcb--,i(n),r=r.next}t.corkedRequestsFree.next=e}(t,e)}}var t;Ts=1,As=C,C.WritableState=_;var r={deprecate:Ms?ks:(Ms=1,ks=d.deprecate)},i=ms(),o=n.Buffer,s=(void 0!==u?u:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var a,c=vs(),l=_s().getHighWaterMark,h=Ss().codes,f=h.ERR_INVALID_ARG_TYPE,p=h.ERR_METHOD_NOT_IMPLEMENTED,m=h.ERR_MULTIPLE_CALLBACK,g=h.ERR_STREAM_CANNOT_PIPE,v=h.ERR_STREAM_DESTROYED,y=h.ERR_STREAM_NULL_VALUES,E=h.ERR_STREAM_WRITE_AFTER_END,b=h.ERR_UNKNOWN_ENCODING,w=c.errorOrDestroy;function S(){}function _(n,r,i){t=t||Ls(),n=n||{},"boolean"!=typeof i&&(i=r instanceof t),this.objectMode=!!n.objectMode,i&&(this.objectMode=this.objectMode||!!n.writableObjectMode),this.highWaterMark=l(this,n,"writableHighWaterMark",i),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===n.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=n.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,r=n.sync,i=n.writecb;if("function"!=typeof i)throw new m;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,r,i){--t.pendingcb,n?(process.nextTick(i,r),process.nextTick(I,e,t),e._writableState.errorEmitted=!0,w(e,r)):(i(r),e._writableState.errorEmitted=!0,w(e,r),I(e,t))}(e,n,r,t,i);else{var o=A(n)||e.destroyed;o||n.corked||n.bufferProcessing||!n.bufferedRequest||M(e,n),r?process.nextTick(k,e,n,o,i):k(e,n,o,i)}}(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==n.emitClose,this.autoDestroy=!!n.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new e(this)}function C(e){var n=this instanceof(t=t||Ls());if(!n&&!a.call(C,this))return new C(e);this._writableState=new _(e,this,n),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),i.call(this)}function x(e,t,n,r,i,o,s){t.writelen=r,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new v("write")):n?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function k(e,t,n,r){n||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,r(),I(e,t)}function M(t,n){n.bufferProcessing=!0;var r=n.bufferedRequest;if(t._writev&&r&&r.next){var i=n.bufferedRequestCount,o=new Array(i),s=n.corkedRequestsFree;s.entry=r;for(var a=0,c=!0;r;)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,x(t,n,!0,n.length,o,"",s.finish),n.pendingcb++,n.lastBufferedRequest=null,s.next?(n.corkedRequestsFree=s.next,s.next=null):n.corkedRequestsFree=new e(n),n.bufferedRequestCount=0}else{for(;r;){var d=r.chunk,l=r.encoding,u=r.callback;if(x(t,n,!1,n.objectMode?1:d.length,d,l,u),r=r.next,n.bufferedRequestCount--,n.writing)break}null===r&&(n.lastBufferedRequest=null)}n.bufferedRequest=r,n.bufferProcessing=!1}function A(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function T(e,t){e._final((function(n){t.pendingcb--,n&&w(e,n),t.prefinished=!0,e.emit("prefinish"),I(e,t)}))}function I(e,t){var n=A(t);if(n&&(function(e,t){t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,process.nextTick(T,e,t)))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var r=e._readableState;(!r||r.autoDestroy&&r.endEmitted)&&e.destroy()}return n}return Ns()(C,i),_.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(_.prototype,"buffer",{get:r.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(a=Function.prototype[Symbol.hasInstance],Object.defineProperty(C,Symbol.hasInstance,{value:function(e){return!!a.call(this,e)||this===C&&(e&&e._writableState instanceof _)}})):a=function(e){return e instanceof this},C.prototype.pipe=function(){w(this,new g)},C.prototype.write=function(e,t,n){var r,i=this._writableState,a=!1,c=!i.objectMode&&(r=e,o.isBuffer(r)||r instanceof s);return c&&!o.isBuffer(e)&&(e=function(e){return o.from(e)}(e)),"function"==typeof t&&(n=t,t=null),c?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof n&&(n=S),i.ending?function(e,t){var n=new E;w(e,n),process.nextTick(t,n)}(this,n):(c||function(e,t,n,r){var i;return null===n?i=new y:"string"==typeof n||t.objectMode||(i=new f("chunk",["string","Buffer"],n)),!i||(w(e,i),process.nextTick(r,i),!1)}(this,i,e,n))&&(i.pendingcb++,a=function(e,t,n,r,i,s){if(!n){var a=function(e,t,n){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=o.from(t,n));return t}(t,r,i);r!==a&&(n=!0,i="buffer",r=a)}var c=t.objectMode?1:r.length;t.length+=c;var d=t.length<t.highWaterMark;d||(t.needDrain=!0);if(t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:s,next:null},l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else x(e,t,!1,c,r,i,s);return d}(this,i,c,e,t,n)),a},C.prototype.cork=function(){this._writableState.corked++},C.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||M(this,e))},C.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new b(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(C.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(C.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),C.prototype._write=function(e,t,n){n(new p("_write()"))},C.prototype._writev=null,C.prototype.end=function(e,t,n){var r=this._writableState;return"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!=e&&this.write(e,t),r.corked&&(r.corked=1,this.uncork()),r.ending||function(e,t,n){t.ending=!0,I(e,t),n&&(t.finished?process.nextTick(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,r,n),this},Object.defineProperty(C.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(C.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),C.prototype.destroy=c.destroy,C.prototype._undestroy=c.undestroy,C.prototype._destroy=function(e,t){t(e)},As}function Ls(){if(Ps)return Is;Ps=1;var e=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};Is=s;var t=oa(),n=Ds();Ns()(s,t);for(var r=e(n.prototype),i=0;i<r.length;i++){var o=r[i];s.prototype[o]||(s.prototype[o]=n.prototype[o])}function s(e){if(!(this instanceof s))return new s(e);t.call(this,e),n.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",a)))}function a(){this._writableState.ended||process.nextTick(c,this)}function c(e){e.end()}return Object.defineProperty(s.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(s.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(s.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(s.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),Is}var Bs,Ks,js,$s,Hs,Us,Fs,qs,zs,Ws,Vs,Gs,Ys,Zs,Js,Qs,Xs={},ea={exports:{}};function ta(){if(Ks)return Xs;Ks=1;var e=(Bs||(Bs=1,function(e,t){var r=n,i=r.Buffer;function o(e,t){for(var n in e)t[n]=e[n]}function s(e,t,n){return i(e,t,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=r:(o(r,t),t.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(e,t,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,n)},s.alloc=function(e,t,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var r=i(e);return void 0!==t?"string"==typeof n?r.fill(t,n):r.fill(t):r.fill(0),r},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}}(ea,ea.exports)),ea.exports).Buffer,t=e.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function r(n){var r;switch(this.encoding=function(n){var r=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(n);if("string"!=typeof r&&(e.isEncoding===t||!t(n)))throw new Error("Unknown encoding: "+n);return r||n}(n),this.encoding){case"utf16le":this.text=s,this.end=a,r=4;break;case"utf8":this.fillLast=o,r=4;break;case"base64":this.text=c,this.end=d,r=3;break;default:return this.write=l,void(this.end=u)}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(r)}function i(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function o(e){var t=this.lastTotal-this.lastNeed,n=function(e,t,n){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==n?n:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function s(e,t){if((e.length-t)%2==0){var n=e.toString("utf16le",t);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function a(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function c(e,t){var n=(e.length-t)%3;return 0===n?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function d(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function l(e){return e.toString(this.encoding)}function u(e){return e&&e.length?this.write(e):""}return Xs.StringDecoder=r,r.prototype.write=function(e){if(0===e.length)return"";var t,n;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""},r.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},r.prototype.text=function(e,t){var n=function(e,t,n){var r=t.length-1;if(r<n)return 0;var o=i(t[r]);if(o>=0)return o>0&&(e.lastNeed=o-1),o;if(--r<n||-2===o)return 0;if(o=i(t[r]),o>=0)return o>0&&(e.lastNeed=o-2),o;if(--r<n||-2===o)return 0;if(o=i(t[r]),o>=0)return o>0&&(2===o?o=0:e.lastNeed=o-3),o;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;var r=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,r),e.toString("utf8",t,r)},r.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length},Xs}function na(){if($s)return js;$s=1;var e=Ss().codes.ERR_STREAM_PREMATURE_CLOSE;function t(){}return js=function n(r,i,o){if("function"==typeof i)return n(r,null,i);i||(i={}),o=function(e){var t=!1;return function(){if(!t){t=!0;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.apply(this,r)}}}(o||t);var s=i.readable||!1!==i.readable&&r.readable,a=i.writable||!1!==i.writable&&r.writable,c=function(){r.writable||l()},d=r._writableState&&r._writableState.finished,l=function(){a=!1,d=!0,s||o.call(r)},u=r._readableState&&r._readableState.endEmitted,h=function(){s=!1,u=!0,a||o.call(r)},f=function(e){o.call(r,e)},p=function(){var t;return s&&!u?(r._readableState&&r._readableState.ended||(t=new e),o.call(r,t)):a&&!d?(r._writableState&&r._writableState.ended||(t=new e),o.call(r,t)):void 0},m=function(){r.req.on("finish",l)};return!function(e){return e.setHeader&&"function"==typeof e.abort}(r)?a&&!r._writableState&&(r.on("end",c),r.on("close",c)):(r.on("complete",l),r.on("abort",p),r.req?m():r.on("request",m)),r.on("end",h),r.on("finish",l),!1!==i.error&&r.on("error",f),r.on("close",p),function(){r.removeListener("complete",l),r.removeListener("abort",p),r.removeListener("request",m),r.req&&r.req.removeListener("finish",l),r.removeListener("end",c),r.removeListener("close",c),r.removeListener("finish",l),r.removeListener("end",h),r.removeListener("error",f),r.removeListener("close",p)}},js}function ra(){if(Us)return Hs;var e;function t(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Us=1;var n=na(),r=Symbol("lastResolve"),i=Symbol("lastReject"),o=Symbol("error"),s=Symbol("ended"),a=Symbol("lastPromise"),c=Symbol("handlePromise"),d=Symbol("stream");function l(e,t){return{value:e,done:t}}function u(e){var t=e[r];if(null!==t){var n=e[d].read();null!==n&&(e[a]=null,e[r]=null,e[i]=null,t(l(n,!1)))}}function h(e){process.nextTick(u,e)}var f=Object.getPrototypeOf((function(){})),p=Object.setPrototypeOf((t(e={get stream(){return this[d]},next:function(){var e=this,t=this[o];if(null!==t)return Promise.reject(t);if(this[s])return Promise.resolve(l(void 0,!0));if(this[d].destroyed)return new Promise((function(t,n){process.nextTick((function(){e[o]?n(e[o]):t(l(void 0,!0))}))}));var n,r=this[a];if(r)n=new Promise(function(e,t){return function(n,r){e.then((function(){t[s]?n(l(void 0,!0)):t[c](n,r)}),r)}}(r,this));else{var i=this[d].read();if(null!==i)return Promise.resolve(l(i,!1));n=new Promise(this[c])}return this[a]=n,n}},Symbol.asyncIterator,(function(){return this})),t(e,"return",(function(){var e=this;return new Promise((function(t,n){e[d].destroy(null,(function(e){e?n(e):t(l(void 0,!0))}))}))})),e),f);return Hs=function(e){var u,f=Object.create(p,(t(u={},d,{value:e,writable:!0}),t(u,r,{value:null,writable:!0}),t(u,i,{value:null,writable:!0}),t(u,o,{value:null,writable:!0}),t(u,s,{value:e._readableState.endEmitted,writable:!0}),t(u,c,{value:function(e,t){var n=f[d].read();n?(f[a]=null,f[r]=null,f[i]=null,e(l(n,!1))):(f[r]=e,f[i]=t)},writable:!0}),u));return f[a]=null,n(e,(function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=f[i];return null!==t&&(f[a]=null,f[r]=null,f[i]=null,t(e)),void(f[o]=e)}var n=f[r];null!==n&&(f[a]=null,f[r]=null,f[i]=null,n(l(void 0,!0))),f[s]=!0})),e.on("readable",h.bind(null,f)),f},Hs}function ia(){if(qs)return Fs;function e(e,t,n,r,i,o,s){try{var a=e[o](s),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(r,i)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}qs=1;var r=Ss().codes.ERR_INVALID_ARG_TYPE;return Fs=function(i,o,s){var a;if(o&&"function"==typeof o.next)a=o;else if(o&&o[Symbol.asyncIterator])a=o[Symbol.asyncIterator]();else{if(!o||!o[Symbol.iterator])throw new r("iterable",["Iterable"],o);a=o[Symbol.iterator]()}var c=new i(function(e){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?t(Object(i),!0).forEach((function(t){n(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}({objectMode:!0},s)),d=!1;function l(){return u.apply(this,arguments)}function u(){var t;return t=function*(){try{var e=yield a.next(),t=e.value;e.done?c.push(null):c.push(yield t)?l():d=!1}catch(e){c.destroy(e)}},u=function(){var n=this,r=arguments;return new Promise((function(i,o){var s=t.apply(n,r);function a(t){e(s,i,o,a,c,"next",t)}function c(t){e(s,i,o,a,c,"throw",t)}a(void 0)}))},u.apply(this,arguments)}return c._read=function(){d||(d=!0,l())},c},Fs}function oa(){if(Ws)return zs;var e;Ws=1,zs=x,x.ReadableState=C,a.EventEmitter;var t=function(e,t){return e.listeners(t).length},r=ms(),i=n.Buffer,o=(void 0!==u?u:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var s,c=d;s=c&&c.debuglog?c.debuglog("stream"):function(){};var l,h,f,p=gs(),m=vs(),g=_s().getHighWaterMark,v=Ss().codes,y=v.ERR_INVALID_ARG_TYPE,E=v.ERR_STREAM_PUSH_AFTER_EOF,b=v.ERR_METHOD_NOT_IMPLEMENTED,w=v.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;Ns()(x,r);var S=m.errorOrDestroy,_=["error","close","destroy","pause","resume"];function C(t,n,r){e=e||Ls(),t=t||{},"boolean"!=typeof r&&(r=n instanceof e),this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=g(this,t,"readableHighWaterMark",r),this.buffer=new p,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(l||(l=ta().StringDecoder),this.decoder=new l(t.encoding),this.encoding=t.encoding)}function x(t){if(e=e||Ls(),!(this instanceof x))return new x(t);var n=this instanceof e;this._readableState=new C(t,this,n),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),r.call(this)}function k(e,t,n,r,a){s("readableAddChunk",t);var c,d=e._readableState;if(null===t)d.reading=!1,function(e,t){if(s("onEofChunk"),t.ended)return;if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,t.sync?I(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,P(e)))}(e,d);else if(a||(c=function(e,t){var n;r=t,i.isBuffer(r)||r instanceof o||"string"==typeof t||void 0===t||e.objectMode||(n=new y("chunk",["string","Buffer","Uint8Array"],t));var r;return n}(d,t)),c)S(e,c);else if(d.objectMode||t&&t.length>0)if("string"==typeof t||d.objectMode||Object.getPrototypeOf(t)===i.prototype||(t=function(e){return i.from(e)}(t)),r)d.endEmitted?S(e,new w):M(e,d,t,!0);else if(d.ended)S(e,new E);else{if(d.destroyed)return!1;d.reading=!1,d.decoder&&!n?(t=d.decoder.write(t),d.objectMode||0!==t.length?M(e,d,t,!1):R(e,d)):M(e,d,t,!1)}else r||(d.reading=!1,R(e,d));return!d.ended&&(d.length<d.highWaterMark||0===d.length)}function M(e,t,n,r){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",n)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&I(e)),R(e,t)}Object.defineProperty(x.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),x.prototype.destroy=m.destroy,x.prototype._undestroy=m.undestroy,x.prototype._destroy=function(e,t){t(e)},x.prototype.push=function(e,t){var n,r=this._readableState;return r.objectMode?n=!0:"string"==typeof e&&((t=t||r.defaultEncoding)!==r.encoding&&(e=i.from(e,t),t=""),n=!0),k(this,e,t,!1,n)},x.prototype.unshift=function(e){return k(this,e,null,!0,!1)},x.prototype.isPaused=function(){return!1===this._readableState.flowing},x.prototype.setEncoding=function(e){l||(l=ta().StringDecoder);var t=new l(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var n=this._readableState.buffer.head,r="";null!==n;)r+=t.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==r&&this._readableState.buffer.push(r),this._readableState.length=r.length,this};var A=1073741824;function T(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=A?e=A:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function I(e){var t=e._readableState;s("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(s("emitReadable",t.flowing),t.emittedReadable=!0,process.nextTick(P,e))}function P(e){var t=e._readableState;s("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,B(e)}function R(e,t){t.readingMore||(t.readingMore=!0,process.nextTick(O,e,t))}function O(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var n=t.length;if(s("maybeReadMore read 0"),e.read(0),n===t.length)break}t.readingMore=!1}function N(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function D(e){s("readable nexttick read 0"),e.read(0)}function L(e,t){s("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),B(e),t.flowing&&!t.reading&&e.read(0)}function B(e){var t=e._readableState;for(s("flow",t.flowing);t.flowing&&null!==e.read(););}function K(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):n=t.buffer.consume(e,t.decoder),n);var n}function j(e){var t=e._readableState;s("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,process.nextTick($,t,e))}function $(e,t){if(s("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var n=t._writableState;(!n||n.autoDestroy&&n.finished)&&t.destroy()}}function H(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}return x.prototype.read=function(e){s("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return s("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?j(this):I(this),null;if(0===(e=T(e,t))&&t.ended)return 0===t.length&&j(this),null;var r,i=t.needReadable;return s("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&s("length less than watermark",i=!0),t.ended||t.reading?s("reading or ended",i=!1):i&&(s("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=T(n,t))),null===(r=e>0?K(e,t):null)?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&j(this)),null!==r&&this.emit("data",r),r},x.prototype._read=function(e){S(this,new b("_read()"))},x.prototype.pipe=function(e,n){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e)}i.pipesCount+=1,s("pipe count=%d opts=%j",i.pipesCount,n);var o=(!n||!1!==n.end)&&e!==process.stdout&&e!==process.stderr?c:m;function a(t,n){s("onunpipe"),t===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,s("cleanup"),e.removeListener("close",f),e.removeListener("finish",p),e.removeListener("drain",d),e.removeListener("error",h),e.removeListener("unpipe",a),r.removeListener("end",c),r.removeListener("end",m),r.removeListener("data",u),l=!0,!i.awaitDrain||e._writableState&&!e._writableState.needDrain||d())}function c(){s("onend"),e.end()}i.endEmitted?process.nextTick(o):r.once("end",o),e.on("unpipe",a);var d=function(e){return function(){var n=e._readableState;s("pipeOnDrain",n.awaitDrain),n.awaitDrain&&n.awaitDrain--,0===n.awaitDrain&&t(e,"data")&&(n.flowing=!0,B(e))}}(r);e.on("drain",d);var l=!1;function u(t){s("ondata");var n=e.write(t);s("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===e||i.pipesCount>1&&-1!==H(i.pipes,e))&&!l&&(s("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function h(n){s("onerror",n),m(),e.removeListener("error",h),0===t(e,"error")&&S(e,n)}function f(){e.removeListener("finish",p),m()}function p(){s("onfinish"),e.removeListener("close",f),m()}function m(){s("unpipe"),r.unpipe(e)}return r.on("data",u),function(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}(e,"error",h),e.once("close",f),e.once("finish",p),e.emit("pipe",r),i.flowing||(s("pipe resume"),r.resume()),e},x.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n)),this;if(!e){var r=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)r[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=H(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n)),this},x.prototype.on=function(e,t){var n=r.prototype.on.call(this,e,t),i=this._readableState;return"data"===e?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===e&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,s("on readable",i.length,i.reading),i.length?I(this):i.reading||process.nextTick(D,this))),n},x.prototype.addListener=x.prototype.on,x.prototype.removeListener=function(e,t){var n=r.prototype.removeListener.call(this,e,t);return"readable"===e&&process.nextTick(N,this),n},x.prototype.removeAllListeners=function(e){var t=r.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||process.nextTick(N,this),t},x.prototype.resume=function(){var e=this._readableState;return e.flowing||(s("resume"),e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,process.nextTick(L,e,t))}(this,e)),e.paused=!1,this},x.prototype.pause=function(){return s("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(s("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},x.prototype.wrap=function(e){var t=this,n=this._readableState,r=!1;for(var i in e.on("end",(function(){if(s("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){(s("wrapped data"),n.decoder&&(i=n.decoder.write(i)),n.objectMode&&null==i)||(n.objectMode||i&&i.length)&&(t.push(i)||(r=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<_.length;o++)e.on(_[o],this.emit.bind(this,_[o]));return this._read=function(t){s("wrapped _read",t),r&&(r=!1,e.resume())},this},"function"==typeof Symbol&&(x.prototype[Symbol.asyncIterator]=function(){return void 0===h&&(h=ra()),h(this)}),Object.defineProperty(x.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(x.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(x.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),x._fromList=K,Object.defineProperty(x.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(x.from=function(e,t){return void 0===f&&(f=ia()),f(x,e,t)}),zs}function sa(){if(Gs)return Vs;Gs=1,Vs=a;var e=Ss().codes,t=e.ERR_METHOD_NOT_IMPLEMENTED,n=e.ERR_MULTIPLE_CALLBACK,r=e.ERR_TRANSFORM_ALREADY_TRANSFORMING,i=e.ERR_TRANSFORM_WITH_LENGTH_0,o=Ls();function s(e,t){var r=this._transformState;r.transforming=!1;var i=r.writecb;if(null===i)return this.emit("error",new n);r.writechunk=null,r.writecb=null,null!=t&&this.push(t),i(e);var o=this._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&this._read(o.highWaterMark)}function a(e){if(!(this instanceof a))return new a(e);o.call(this,e),this._transformState={afterTransform:s.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",c)}function c(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?d(this,null,null):this._flush((function(t,n){d(e,t,n)}))}function d(e,t,n){if(t)return e.emit("error",t);if(null!=n&&e.push(n),e._writableState.length)throw new i;if(e._transformState.transforming)throw new r;return e.push(null)}return Ns()(a,o),a.prototype.push=function(e,t){return this._transformState.needTransform=!1,o.prototype.push.call(this,e,t)},a.prototype._transform=function(e,n,r){r(new t("_transform()"))},a.prototype._write=function(e,t,n){var r=this._transformState;if(r.writecb=n,r.writechunk=e,r.writeencoding=t,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},a.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},a.prototype._destroy=function(e,t){o.prototype._destroy.call(this,e,(function(e){t(e)}))},Vs}function aa(){if(Qs)return Js;var e;Qs=1;var t=Ss().codes,n=t.ERR_MISSING_ARGS,r=t.ERR_STREAM_DESTROYED;function i(e){if(e)throw e}function o(e){e()}function s(e,t){return e.pipe(t)}return Js=function(){for(var t=arguments.length,a=new Array(t),c=0;c<t;c++)a[c]=arguments[c];var d,l=function(e){return e.length?"function"!=typeof e[e.length-1]?i:e.pop():i}(a);if(Array.isArray(a[0])&&(a=a[0]),a.length<2)throw new n("streams");var u=a.map((function(t,n){var i=n<a.length-1;return function(t,n,i,o){o=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(o);var s=!1;t.on("close",(function(){s=!0})),void 0===e&&(e=na()),e(t,{readable:n,writable:i},(function(e){if(e)return o(e);s=!0,o()}));var a=!1;return function(e){if(!s&&!a)return a=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(t)?t.abort():"function"==typeof t.destroy?t.destroy():void o(e||new r("pipe"))}}(t,i,n>0,(function(e){d||(d=e),e&&u.forEach(o),i||(u.forEach(o),l(d))}))}));return a.reduce(s)},Js}!function(e,t){var n=c;"disable"===process.env.READABLE_STREAM&&n?(e.exports=n.Readable,Object.assign(e.exports,n),e.exports.Stream=n):((t=e.exports=oa()).Stream=n||t,t.Readable=t,t.Writable=Ds(),t.Duplex=Ls(),t.Transform=sa(),t.PassThrough=function(){if(Zs)return Ys;Zs=1,Ys=t;var e=sa();function t(n){if(!(this instanceof t))return new t(n);e.call(this,n)}return Ns()(t,e),t.prototype._transform=function(e,t,n){n(null,e)},Ys}(),t.finished=na(),t.pipeline=aa())}(ps,ps.exports);var ca=ps.exports;function da(e={}){const t={},n=new ca.Duplex({objectMode:!0,read:()=>{},write:function(n,o,s){let a=null;try{!n.id?function(n){e?.retryOnMessage&&n.method===e.retryOnMessage&&Object.values(t).forEach((({req:e,retryCount:n=0})=>{if(!e.id)return;if(n>=3)throw new Error(`StreamMiddleware - Retry limit exceeded for request id "${e.id}"`);const r=t[e.id];r&&(r.retryCount=n+1),i(e)}));r.emit("notification",n)}(n):function(e){const{id:n}=e;if(null===n)return;const r=t[n];if(!r)return void console.warn(`StreamMiddleware - Unknown response id "${n}"`);delete t[n],Object.assign(r.res,e),setTimeout(r.end)}(n)}catch(e){a=e}s(a)}}),r=new Bo;return{events:r,middleware:(e,n,r,o)=>{t[e.id]={req:e,res:n,next:r,end:o},i(e)},stream:n};function i(e){n.push(e)}}var la={},ua={exports:{}},ha=function e(t,n){if(t&&n)return e(t)(n);if("function"!=typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){r[e]=t[e]})),r;function r(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];var r=t.apply(this,e),i=e[e.length-1];return"function"==typeof r&&r!==i&&Object.keys(i).forEach((function(e){r[e]=i[e]})),r}};var fa=ha;function pa(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function ma(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},n=e.name||"Function wrapped with `once`";return t.onceError=n+" shouldn't be called more than once",t.called=!1,t}ua.exports=fa(pa),ua.exports.strict=fa(ma),pa.proto=pa((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return pa(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return ma(this)},configurable:!0})}));var ga=ua.exports,va={};Object.defineProperty(va,"__esModule",{value:!0}),va.Substream=void 0;const ya=ca;class Ea extends ya.Duplex{constructor({parent:e,name:t}){super({objectMode:!0}),this._parent=e,this._name=t}_read(){}_write(e,t,n){this._parent.push({name:this._name,data:e}),n()}}va.Substream=Ea;var ba=u&&u.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(la,"__esModule",{value:!0}),la.ObjectMultiplex=void 0;const wa=ca,Sa=ba(ga),_a=va,Ca=Symbol("IGNORE_SUBSTREAM");let xa=class extends wa.Duplex{constructor(e={}){super(Object.assign(Object.assign({},e),{objectMode:!0})),this._substreams={}}createStream(e){if(this.destroyed)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already destroyed`);if(this._readableState.ended||this._writableState.ended)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already ended`);if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);const t=new _a.Substream({parent:this,name:e});return this._substreams[e]=t,function(e,t){const n=(0,Sa.default)(t);(0,wa.finished)(e,{readable:!1},n),(0,wa.finished)(e,{writable:!1},n)}(this,(e=>t.destroy(e||void 0))),t}ignoreStream(e){if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);this._substreams[e]=Ca}_read(){}_write(e,t,n){const{name:r,data:i}=e;if(!r)return console.warn(`ObjectMultiplex - malformed chunk without name "${e}"`),n();const o=this._substreams[r];return o?(o!==Ca&&o.push(i),n()):(console.warn(`ObjectMultiplex - orphaned data for stream "${r}"`),n())}};la.ObjectMultiplex=xa;var ka=h(la.ObjectMultiplex);const Ma=e=>null!==e&&"object"==typeof e&&"function"==typeof e.pipe;Ma.writable=e=>Ma(e)&&!1!==e.writable&&"function"==typeof e._write&&"object"==typeof e._writableState,Ma.readable=e=>Ma(e)&&!1!==e.readable&&"function"==typeof e._read&&"object"==typeof e._readableState,Ma.duplex=e=>Ma.writable(e)&&Ma.readable(e),Ma.transform=e=>Ma.duplex(e)&&"function"==typeof e._transform;var Aa,Ta=Ma,Ia=class extends fs{constructor(e,{jsonRpcStreamName:t,logger:n=console,maxEventListeners:r=100,rpcMiddleware:i=[]}){if(super({logger:n,maxEventListeners:r,rpcMiddleware:i}),!Ta.duplex(e))throw new Error($r.errors.invalidDuplexStream());this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this);const o=new ka;ca.pipeline(e,o,e,this._handleStreamDisconnect.bind(this,"MetaMask")),this._jsonRpcConnection=da({retryOnMessage:"METAMASK_EXTENSION_CONNECT_CAN_RETRY"}),ca.pipeline(this._jsonRpcConnection.stream,o.createStream(t),this._jsonRpcConnection.stream,this._handleStreamDisconnect.bind(this,"MetaMask RpcProvider")),this._rpcEngine.push(this._jsonRpcConnection.middleware),this._jsonRpcConnection.events.on("notification",(t=>{const{method:n,params:r}=t;"metamask_accountsChanged"===n?this._handleAccountsChanged(r):"metamask_unlockStateChanged"===n?this._handleUnlockStateChanged(r):"metamask_chainChanged"===n?this._handleChainChanged(r):Ho.includes(n)?this.emit("message",{type:n,data:r}):"METAMASK_STREAM_FAILURE"===n&&e.destroy(new Error($r.errors.permanentlyDisconnected()))}))}async _initializeStateAsync(){let e;try{e=await this.request({method:"metamask_getProviderState"})}catch(e){this._log.error("MetaMask: Failed to get initial state. Please report this bug.",e)}this._initializeState(e)}_handleStreamDisconnect(e,t){let n=`MetaMask: Lost connection to "${e}".`;t?.stack&&(n+=`\n${t.stack}`),this._log.warn(n),this.listenerCount("error")>0&&this.emit("error",n),this._handleDisconnect(!1,t?t.message:void 0)}_handleChainChanged({chainId:e,networkVersion:t}={}){qo(e)&&(e=>Boolean(e)&&"string"==typeof e)(t)?"loading"===t?this._handleDisconnect(!0):super._handleChainChanged({chainId:e}):this._log.error($r.errors.invalidNetworkParams(),{chainId:e,networkVersion:t})}},Pa=class extends Ia{constructor(e,{jsonRpcStreamName:t="metamask-provider",logger:n=console,maxEventListeners:r=100,shouldSendMetadata:i}={}){if(super(e,{jsonRpcStreamName:t,logger:n,maxEventListeners:r,rpcMiddleware:Uo(n)}),this._sentWarnings={chainId:!1,networkVersion:!1,selectedAddress:!1,enable:!1,experimentalMethods:!1,send:!1,events:{close:!1,data:!1,networkChanged:!1,notification:!1}},Qo(this,Aa,void 0),this._initializeStateAsync(),Xo(this,Aa,null),this.isMetaMask=!0,this._sendSync=this._sendSync.bind(this),this.enable=this.enable.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this._warnOfDeprecation=this._warnOfDeprecation.bind(this),this._metamask=this._getExperimentalApi(),this._jsonRpcConnection.events.on("notification",(e=>{const{method:t}=e;Ho.includes(t)&&(this.emit("data",e),this.emit("notification",e.params.result))})),i)if("complete"===document.readyState)Wo(this._rpcEngine,this._log);else{const e=()=>{Wo(this._rpcEngine,this._log),window.removeEventListener("DOMContentLoaded",e)};window.addEventListener("DOMContentLoaded",e)}}get chainId(){return this._sentWarnings.chainId||(this._log.warn($r.warnings.chainIdDeprecation),this._sentWarnings.chainId=!0),super.chainId}get networkVersion(){return this._sentWarnings.networkVersion||(this._log.warn($r.warnings.networkVersionDeprecation),this._sentWarnings.networkVersion=!0),Jo(this,Aa)}get selectedAddress(){return this._sentWarnings.selectedAddress||(this._log.warn($r.warnings.selectedAddressDeprecation),this._sentWarnings.selectedAddress=!0),super.selectedAddress}sendAsync(e,t){this._rpcRequest(e,t)}addListener(e,t){return this._warnOfDeprecation(e),super.addListener(e,t)}on(e,t){return this._warnOfDeprecation(e),super.on(e,t)}once(e,t){return this._warnOfDeprecation(e),super.once(e,t)}prependListener(e,t){return this._warnOfDeprecation(e),super.prependListener(e,t)}prependOnceListener(e,t){return this._warnOfDeprecation(e),super.prependOnceListener(e,t)}_handleDisconnect(e,t){super._handleDisconnect(e,t),Jo(this,Aa)&&!e&&Xo(this,Aa,null)}_warnOfDeprecation(e){!1===this._sentWarnings?.events[e]&&(this._log.warn($r.warnings.events[e]),this._sentWarnings.events[e]=!0)}async enable(){return this._sentWarnings.enable||(this._log.warn($r.warnings.enableDeprecation),this._sentWarnings.enable=!0),new Promise(((e,t)=>{try{this._rpcRequest({method:"eth_requestAccounts",params:[]},Fo(e,t))}catch(e){t(e)}}))}send(e,t){return this._sentWarnings.send||(this._log.warn($r.warnings.sendDeprecation),this._sentWarnings.send=!0),"string"!=typeof e||t&&!Array.isArray(t)?e&&"object"==typeof e&&"function"==typeof t?this._rpcRequest(e,t):this._sendSync(e):new Promise(((n,r)=>{try{this._rpcRequest({method:e,params:t},Fo(n,r,!1))}catch(e){r(e)}}))}_sendSync(e){let t;switch(e.method){case"eth_accounts":t=this.selectedAddress?[this.selectedAddress]:[];break;case"eth_coinbase":t=this.selectedAddress??null;break;case"eth_uninstallFilter":this._rpcRequest(e,zo),t=!0;break;case"net_version":t=Jo(this,Aa)??null;break;default:throw new Error($r.errors.unsupportedSync(e.method))}return{id:e.id,jsonrpc:e.jsonrpc,result:t}}_getExperimentalApi(){return new Proxy({isUnlocked:async()=>(this._state.initialized||await new Promise((e=>{this.on("_initialized",(()=>e()))})),this._state.isUnlocked),requestBatch:async e=>{if(!Array.isArray(e))throw ho({message:"Batch requests must be made with an array of request objects.",data:e});return new Promise(((t,n)=>{this._rpcRequest(e,Fo(t,n))}))}},{get:(e,t,...n)=>(this._sentWarnings.experimentalMethods||(this._log.warn($r.warnings.experimentalMethods),this._sentWarnings.experimentalMethods=!0),Reflect.get(e,t,...n))})}_handleChainChanged({chainId:e,networkVersion:t}={}){super._handleChainChanged({chainId:e,networkVersion:t}),this._state.isConnected&&t!==Jo(this,Aa)&&(Xo(this,Aa,t),this._state.initialized&&this.emit("networkChanged",Jo(this,Aa)))}};Aa=new WeakMap;const Ra=t("MM_SDK");Ra.color="#FFAC1C";var Oa={},Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.EthereumProviderError=Na.EthereumRpcError=void 0;const Da=Yi;class La extends Error{constructor(e,t,n){if(!Number.isInteger(e))throw new Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw new Error('"message" must be a nonempty string.');super(t),this.code=e,void 0!==n&&(this.data=n)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}toString(){return Da.default(this.serialize(),Ba,2)}}Na.EthereumRpcError=La;function Ba(e,t){if("[Circular]"!==t)return t}Na.EthereumProviderError=class extends La{constructor(e,t,n){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,n)}};var Ka={},ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.errorValues=ja.errorCodes=void 0,ja.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},ja.errorValues={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.serializeError=e.isValidCode=e.getMessageFromCode=e.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const t=ja,n=Na,r=t.errorCodes.rpc.internal,i="Unspecified error message. This is a bug, please report it.",o={code:r,message:s(r)};function s(n,r=i){if(Number.isInteger(n)){const r=n.toString();if(l(t.errorValues,r))return t.errorValues[r].message;if(c(n))return e.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function a(e){if(!Number.isInteger(e))return!1;const n=e.toString();return!!t.errorValues[n]||!!c(e)}function c(e){return e>=-32099&&e<=-32e3}function d(e){return e&&"object"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function l(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.JSON_RPC_SERVER_ERROR_MESSAGE="Unspecified server error.",e.getMessageFromCode=s,e.isValidCode=a,e.serializeError=function(e,{fallbackError:t=o,shouldIncludeStack:r=!1}={}){var i,c;if(!t||!Number.isInteger(t.code)||"string"!=typeof t.message)throw new Error("Must provide fallback error with integer number code and string message.");if(e instanceof n.EthereumRpcError)return e.serialize();const u={};if(e&&"object"==typeof e&&!Array.isArray(e)&&l(e,"code")&&a(e.code)){const t=e;u.code=t.code,t.message&&"string"==typeof t.message?(u.message=t.message,l(t,"data")&&(u.data=t.data)):(u.message=s(u.code),u.data={originalError:d(e)})}else{u.code=t.code;const n=null===(i=e)||void 0===i?void 0:i.message;u.message=n&&"string"==typeof n?n:t.message,u.data={originalError:d(e)}}const h=null===(c=e)||void 0===c?void 0:c.stack;return r&&e&&h&&"string"==typeof h&&(u.stack=h),u}}(Ka);var $a={};Object.defineProperty($a,"__esModule",{value:!0}),$a.ethErrors=void 0;const Ha=Na,Ua=Ka,Fa=ja;function qa(e,t){const[n,r]=Wa(t);return new Ha.EthereumRpcError(e,n||Ua.getMessageFromCode(e),r)}function za(e,t){const[n,r]=Wa(t);return new Ha.EthereumProviderError(e,n||Ua.getMessageFromCode(e),r)}function Wa(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:n}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t||void 0,n]}}return[]}$a.ethErrors={rpc:{parse:e=>qa(Fa.errorCodes.rpc.parse,e),invalidRequest:e=>qa(Fa.errorCodes.rpc.invalidRequest,e),invalidParams:e=>qa(Fa.errorCodes.rpc.invalidParams,e),methodNotFound:e=>qa(Fa.errorCodes.rpc.methodNotFound,e),internal:e=>qa(Fa.errorCodes.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return qa(t,e)},invalidInput:e=>qa(Fa.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>qa(Fa.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>qa(Fa.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>qa(Fa.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>qa(Fa.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>qa(Fa.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>za(Fa.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>za(Fa.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>za(Fa.errorCodes.provider.unsupportedMethod,e),disconnected:e=>za(Fa.errorCodes.provider.disconnected,e),chainDisconnected:e=>za(Fa.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum Provider custom errors must provide single object argument.");const{code:t,message:n,data:r}=e;if(!n||"string"!=typeof n)throw new Error('"message" must be a nonempty string');return new Ha.EthereumProviderError(t,n,r)}}},function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getMessageFromCode=e.serializeError=e.EthereumProviderError=e.EthereumRpcError=e.ethErrors=e.errorCodes=void 0;const t=Na;Object.defineProperty(e,"EthereumRpcError",{enumerable:!0,get:function(){return t.EthereumRpcError}}),Object.defineProperty(e,"EthereumProviderError",{enumerable:!0,get:function(){return t.EthereumProviderError}});const n=Ka;Object.defineProperty(e,"serializeError",{enumerable:!0,get:function(){return n.serializeError}}),Object.defineProperty(e,"getMessageFromCode",{enumerable:!0,get:function(){return n.getMessageFromCode}});const r=$a;Object.defineProperty(e,"ethErrors",{enumerable:!0,get:function(){return r.ethErrors}});const i=ja;Object.defineProperty(e,"errorCodes",{enumerable:!0,get:function(){return i.errorCodes}})}(Oa);const Va={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Ga={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},Ya={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},Za={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},Ja={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class Qa{static getFirstMatch(e,t){const n=t.match(e);return n&&n.length>0&&n[1]||""}static getSecondMatch(e,t){const n=t.match(e);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(e,t,n){if(e.test(t))return n}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const t=e.split(".").splice(0,2).map((e=>parseInt(e,10)||0));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const t=e.split(".").splice(0,2).map((e=>parseInt(e,10)||0));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,t,n=!1){const r=Qa.getVersionPrecision(e),i=Qa.getVersionPrecision(t);let o=Math.max(r,i),s=0;const a=Qa.map([e,t],(e=>{const t=o-Qa.getVersionPrecision(e),n=e+new Array(t+1).join(".0");return Qa.map(n.split("."),(e=>new Array(20-e.length).join("0")+e)).reverse()}));for(n&&(s=o-Math.min(r,i)),o-=1;o>=s;){if(a[0][o]>a[1][o])return 1;if(a[0][o]===a[1][o]){if(o===s)return 0;o-=1}else if(a[0][o]<a[1][o])return-1}}static map(e,t){const n=[];let r;if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n}static find(e,t){let n,r;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(n=0,r=e.length;n<r;n+=1){const r=e[n];if(t(r,n))return r}}static assign(e,...t){const n=e;let r,i;if(Object.assign)return Object.assign(e,...t);for(r=0,i=t.length;r<i;r+=1){const e=t[r];if("object"==typeof e&&null!==e){Object.keys(e).forEach((t=>{n[t]=e[t]}))}}return e}static getBrowserAlias(e){return Va[e]}static getBrowserTypeByAlias(e){return Ga[e]||""}}const Xa=/version\/(\d+(\.?_?\d+)+)/i,ec=[{test:[/googlebot/i],describe(e){const t={name:"Googlebot"},n=Qa.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/opera/i],describe(e){const t={name:"Opera"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opr\/|opios/i],describe(e){const t={name:"Opera"},n=Qa.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/SamsungBrowser/i],describe(e){const t={name:"Samsung Internet for Android"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Whale/i],describe(e){const t={name:"NAVER Whale Browser"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MZBrowser/i],describe(e){const t={name:"MZ Browser"},n=Qa.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/focus/i],describe(e){const t={name:"Focus"},n=Qa.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/swing/i],describe(e){const t={name:"Swing"},n=Qa.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/coast/i],describe(e){const t={name:"Opera Coast"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){const t={name:"Opera Touch"},n=Qa.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/yabrowser/i],describe(e){const t={name:"Yandex Browser"},n=Qa.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/ucbrowser/i],describe(e){const t={name:"UC Browser"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Maxthon|mxios/i],describe(e){const t={name:"Maxthon"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/epiphany/i],describe(e){const t={name:"Epiphany"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/puffin/i],describe(e){const t={name:"Puffin"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sleipnir/i],describe(e){const t={name:"Sleipnir"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/k-meleon/i],describe(e){const t={name:"K-Meleon"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/micromessenger/i],describe(e){const t={name:"WeChat"},n=Qa.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/qqbrowser/i],describe(e){const t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},n=Qa.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/msie|trident/i],describe(e){const t={name:"Internet Explorer"},n=Qa.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/\sedg\//i],describe(e){const t={name:"Microsoft Edge"},n=Qa.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/edg([ea]|ios)/i],describe(e){const t={name:"Microsoft Edge"},n=Qa.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/vivaldi/i],describe(e){const t={name:"Vivaldi"},n=Qa.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/seamonkey/i],describe(e){const t={name:"SeaMonkey"},n=Qa.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sailfish/i],describe(e){const t={name:"Sailfish"},n=Qa.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return n&&(t.version=n),t}},{test:[/silk/i],describe(e){const t={name:"Amazon Silk"},n=Qa.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/phantom/i],describe(e){const t={name:"PhantomJS"},n=Qa.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/slimerjs/i],describe(e){const t={name:"SlimerJS"},n=Qa.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t={name:"BlackBerry"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(web|hpw)[o0]s/i],describe(e){const t={name:"WebOS Browser"},n=Qa.getFirstMatch(Xa,e)||Qa.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/bada/i],describe(e){const t={name:"Bada"},n=Qa.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/tizen/i],describe(e){const t={name:"Tizen"},n=Qa.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/qupzilla/i],describe(e){const t={name:"QupZilla"},n=Qa.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){const t={name:"Firefox"},n=Qa.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/electron/i],describe(e){const t={name:"Electron"},n=Qa.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MiuiBrowser/i],describe(e){const t={name:"Miui"},n=Qa.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/chromium/i],describe(e){const t={name:"Chromium"},n=Qa.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/chrome|crios|crmo/i],describe(e){const t={name:"Chrome"},n=Qa.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/GSA/i],describe(e){const t={name:"Google Search"},n=Qa.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t={name:"Android Browser"},n=Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/playstation 4/i],describe(e){const t={name:"PlayStation 4"},n=Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/safari|applewebkit/i],describe(e){const t={name:"Safari"},n=Qa.getFirstMatch(Xa,e);return n&&(t.version=n),t}},{test:[/.*/i],describe(e){const t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:Qa.getFirstMatch(t,e),version:Qa.getSecondMatch(t,e)}}}];var tc=[{test:[/Roku\/DVP/],describe(e){const t=Qa.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:Za.Roku,version:t}}},{test:[/windows phone/i],describe(e){const t=Qa.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:Za.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){const t=Qa.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),n=Qa.getWindowsVersionName(t);return{name:Za.Windows,version:t,versionName:n}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){const t={name:Za.iOS},n=Qa.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return n&&(t.version=n),t}},{test:[/macintosh/i],describe(e){const t=Qa.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),n=Qa.getMacOSVersionName(t),r={name:Za.MacOS,version:t};return n&&(r.versionName=n),r}},{test:[/(ipod|iphone|ipad)/i],describe(e){const t=Qa.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:Za.iOS,version:t}}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t=Qa.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),n=Qa.getAndroidVersionName(t),r={name:Za.Android,version:t};return n&&(r.versionName=n),r}},{test:[/(web|hpw)[o0]s/i],describe(e){const t=Qa.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),n={name:Za.WebOS};return t&&t.length&&(n.version=t),n}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t=Qa.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||Qa.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||Qa.getFirstMatch(/\bbb(\d+)/i,e);return{name:Za.BlackBerry,version:t}}},{test:[/bada/i],describe(e){const t=Qa.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:Za.Bada,version:t}}},{test:[/tizen/i],describe(e){const t=Qa.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:Za.Tizen,version:t}}},{test:[/linux/i],describe:()=>({name:Za.Linux})},{test:[/CrOS/],describe:()=>({name:Za.ChromeOS})},{test:[/PlayStation 4/],describe(e){const t=Qa.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:Za.PlayStation4,version:t}}}],nc=[{test:[/googlebot/i],describe:()=>({type:"bot",vendor:"Google"})},{test:[/huawei/i],describe(e){const t=Qa.getFirstMatch(/(can-l01)/i,e)&&"Nova",n={type:Ya.mobile,vendor:"Huawei"};return t&&(n.model=t),n}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:()=>({type:Ya.tablet,vendor:"Nexus"})},{test:[/ipad/i],describe:()=>({type:Ya.tablet,vendor:"Apple",model:"iPad"})},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:()=>({type:Ya.tablet,vendor:"Apple",model:"iPad"})},{test:[/kftt build/i],describe:()=>({type:Ya.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"})},{test:[/silk/i],describe:()=>({type:Ya.tablet,vendor:"Amazon"})},{test:[/tablet(?! pc)/i],describe:()=>({type:Ya.tablet})},{test(e){const t=e.test(/ipod|iphone/i),n=e.test(/like (ipod|iphone)/i);return t&&!n},describe(e){const t=Qa.getFirstMatch(/(ipod|iphone)/i,e);return{type:Ya.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:()=>({type:Ya.mobile,vendor:"Nexus"})},{test:[/[^-]mobi/i],describe:()=>({type:Ya.mobile})},{test:e=>"blackberry"===e.getBrowserName(!0),describe:()=>({type:Ya.mobile,vendor:"BlackBerry"})},{test:e=>"bada"===e.getBrowserName(!0),describe:()=>({type:Ya.mobile})},{test:e=>"windows phone"===e.getBrowserName(),describe:()=>({type:Ya.mobile,vendor:"Microsoft"})},{test(e){const t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:()=>({type:Ya.tablet})},{test:e=>"android"===e.getOSName(!0),describe:()=>({type:Ya.mobile})},{test:e=>"macos"===e.getOSName(!0),describe:()=>({type:Ya.desktop,vendor:"Apple"})},{test:e=>"windows"===e.getOSName(!0),describe:()=>({type:Ya.desktop})},{test:e=>"linux"===e.getOSName(!0),describe:()=>({type:Ya.desktop})},{test:e=>"playstation 4"===e.getOSName(!0),describe:()=>({type:Ya.tv})},{test:e=>"roku"===e.getOSName(!0),describe:()=>({type:Ya.tv})}],rc=[{test:e=>"microsoft edge"===e.getBrowserName(!0),describe(e){if(/\sedg\//i.test(e))return{name:Ja.Blink};const t=Qa.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:Ja.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){const t={name:Ja.Trident},n=Qa.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:e=>e.test(/presto/i),describe(e){const t={name:Ja.Presto},n=Qa.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=e.test(/gecko/i),n=e.test(/like gecko/i);return t&&!n},describe(e){const t={name:Ja.Gecko},n=Qa.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(apple)?webkit\/537\.36/i],describe:()=>({name:Ja.Blink})},{test:[/(apple)?webkit/i],describe(e){const t={name:Ja.WebKit},n=Qa.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}}];class ic{constructor(e,t=!1){if(null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=Qa.find(ec,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=Qa.find(tc,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};const e=Qa.find(nc,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=Qa.find(rc,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return Qa.assign({},this.parsedResult)}satisfies(e){const t={};let n=0;const r={};let i=0;if(Object.keys(e).forEach((o=>{const s=e[o];"string"==typeof s?(r[o]=s,i+=1):"object"==typeof s&&(t[o]=s,n+=1)})),n>0){const e=Object.keys(t),n=Qa.find(e,(e=>this.isOS(e)));if(n){const e=this.satisfies(t[n]);if(void 0!==e)return e}const r=Qa.find(e,(e=>this.isPlatform(e)));if(r){const e=this.satisfies(t[r]);if(void 0!==e)return e}}if(i>0){const e=Object.keys(r),t=Qa.find(e,(e=>this.isBrowser(e,!0)));if(void 0!==t)return this.compareVersion(r[t])}}isBrowser(e,t=!1){const n=this.getBrowserName().toLowerCase();let r=e.toLowerCase();const i=Qa.getBrowserTypeByAlias(r);return t&&i&&(r=i.toLowerCase()),r===n}compareVersion(e){let t=[0],n=e,r=!1;const i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(n=e.substr(1),"="===e[1]?(r=!0,n=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?n=e.substr(1):"~"===e[0]&&(r=!0,n=e.substr(1)),t.indexOf(Qa.compareVersions(i,n,r))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some((e=>this.is(e)))}}class oc{static getParser(e,t=!1){if("string"!=typeof e)throw new Error("UserAgent should be a string");return new ic(e,t)}static parse(e){return new ic(e).getResult()}static get BROWSER_MAP(){return Ga}static get ENGINE_MAP(){return Ja}static get OS_MAP(){return Za}static get PLATFORMS_MAP(){return Ya}}const sc={Initialized:"initialized",DisplayURI:"display_uri",ProviderUpdate:"provider_update",ConnectWithResponse:"connectWithResponse",ConnectionStatus:"connection_status",ServiceStatus:"service_status"};class ac{constructor({shouldSetOnWindow:e,connectionStream:t,shouldSendMetadata:n=!1,shouldShimWeb3:r,sdkInstance:i}){const o=new lc({connectionStream:t,shouldSendMetadata:n,shouldSetOnWindow:e,shouldShimWeb3:r,autoRequestAccounts:!1}),s=new Proxy(o,{deleteProperty:()=>!0});if(this.provider=s,this.sdkInstance=i,e&&"undefined"!=typeof window)try{a=o,window.ethereum=a,window.dispatchEvent(new Event("ethereum#initialized"))}catch(e){Ra("[Ethereum] Unable to set global provider - window.ethereum may be read-only",e)}var a;if(r&&"undefined"!=typeof window)try{!function(e,t=console){let n=!1,r=!1;if(!window.web3){const i="__isMetaMaskShim__";let o={currentProvider:e};Object.defineProperty(o,i,{value:!0,enumerable:!0,configurable:!1,writable:!1}),o=new Proxy(o,{get:(o,s,...a)=>("currentProvider"!==s||n?"currentProvider"===s||s===i||r||(r=!0,t.error("MetaMask no longer injects web3. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3"),e.request({method:"metamask_logWeb3ShimUsage"}).catch((e=>{t.debug("MetaMask: Failed to log web3 shim usage.",e)}))):(n=!0,t.warn("You are accessing the MetaMask window.web3.currentProvider shim. This property is deprecated; use window.ethereum instead. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3")),Reflect.get(o,s,...a)),set:(...e)=>(t.warn("You are accessing the MetaMask window.web3 shim. This object is deprecated; use window.ethereum instead. For details, see: https://docs.metamask.io/guide/provider-migration.html#replacing-window-web3"),Reflect.set(...e))}),Object.defineProperty(window,"web3",{value:o,enumerable:!1,configurable:!0,writable:!0})}}(this.provider)}catch(e){Ra("[Ethereum] Unable to shim web3 - window.web3 may be read-only",e)}this.provider.on("display_uri",(e=>{this.sdkInstance.emit(sc.DisplayURI,e)})),this.provider.on("_initialized",(()=>{const e={chainId:this.provider.getChainId(),isConnected:this.provider.isConnected(),isMetaMask:this.provider.isMetaMask,selectedAddress:this.provider.getSelectedAddress(),networkVersion:this.provider.getNetworkVersion()};this.sdkInstance.emit(sc.Initialized,e),Ra("[Ethereum: constructor()] provider initialized",e)}))}static init(e){var t;return Ra("[Ethereum: init()] Initializing Ethereum service"),this.instance=new ac(e),null===(t=this.instance)||void 0===t?void 0:t.provider}static destroy(){}static getInstance(){var e;if(!(null===(e=this.instance)||void 0===e?void 0:e.provider))throw new Error("Ethereum instance not intiialized - call Ethereum.factory first.");return this.instance}static getProvider(){var e;if(!(null===(e=this.instance)||void 0===e?void 0:e.provider))throw new Error("Ethereum instance not intiialized - call Ethereum.factory first.");return this.instance.provider}}class cc{constructor({useDeepLink:e,preferredOpenLink:t,debug:n=!1}){this.state={platformType:void 0,useDeeplink:!1,preferredOpenLink:void 0,debug:!1},this.state.platformType=this.getPlatformType(),this.state.useDeeplink=e,this.state.preferredOpenLink=t,this.state.debug=n}openDeeplink(e,t,n){return function(e,t,n,r){const{state:i}=e;Ra(`[PlatfformManager: openDeeplink()] universalLink --\x3e ${t}`),Ra(`[PlatfformManager: openDeeplink()] deepLink --\x3e ${n}`);try{if(i.preferredOpenLink)return void i.preferredOpenLink(i.useDeeplink?n:t,r);if(Ra(`[PlatfformManager: openDeeplink()] open link now useDeepLink=${i.useDeeplink} link=${i.useDeeplink?n:t}`),i.useDeeplink)"undefined"!=typeof window&&(window.location.href=n);else if("undefined"!=typeof document){const e=document.createElement("a");e.href=t,e.target="_self",e.rel="noreferrer noopener",e.click()}}catch(e){console.log("[PlatfformManager: openDeeplink()] can't open link",e)}}(this,e,t,n)}isReactNative(){var e;return this.isNotBrowser()&&"undefined"!=typeof window&&(null===window||void 0===window?void 0:window.navigator)&&"ReactNative"===(null===(e=window.navigator)||void 0===e?void 0:e.product)}isMetaMaskInstalled(){return function(){const e=ac.getProvider()||(null===window||void 0===window?void 0:window.ethereum);return Ra(`[PlatfformManager: isMetaMaskInstalled()] isMetaMask=${null==e?void 0:e.isMetaMask} isConnected=${null==e?void 0:e.isConnected()}`),(null==e?void 0:e.isMetaMask)&&(null==e?void 0:e.isConnected())}()}isDesktopWeb(){return this.isBrowser()&&!this.isMobileWeb()}isMobile(){var e,t;const n=oc.parse(window.navigator.userAgent);return"mobile"===(null===(e=null==n?void 0:n.platform)||void 0===e?void 0:e.type)||"tablet"===(null===(t=null==n?void 0:n.platform)||void 0===t?void 0:t.type)}isSecure(){return this.isReactNative()||this.isMobileWeb()}isMetaMaskMobileWebView(){return"undefined"!=typeof window&&(Boolean(window.ReactNativeWebView)&&Boolean(navigator.userAgent.endsWith("MetaMaskMobile")))}isMobileWeb(){return this.state.platformType===exports.PlatformType.MobileWeb}static isNotBrowser(){var e;return"undefined"==typeof window||!(null===window||void 0===window?void 0:window.navigator)||"undefined"!=typeof global&&"ReactNative"===(null===(e=null===global||void 0===global?void 0:global.navigator)||void 0===e?void 0:e.product)||"ReactNative"===(null===navigator||void 0===navigator?void 0:navigator.product)}isNotBrowser(){return cc.isNotBrowser()}static isBrowser(){return!this.isNotBrowser()}isBrowser(){return cc.isBrowser()}isNodeJS(){return this.isNotBrowser()&&!this.isReactNative()}isUseDeepLink(){return this.state.useDeeplink}getPlatformType(){return function(e){const{state:t}=e;return t.platformType?t.platformType:e.isReactNative()?exports.PlatformType.ReactNative:e.isNotBrowser()?exports.PlatformType.NonBrowser:e.isMetaMaskMobileWebView()?exports.PlatformType.MetaMaskMobileWebview:e.isMobile()?exports.PlatformType.MobileWeb:exports.PlatformType.DesktopWeb}(this)}}const dc=e=>l(void 0,void 0,void 0,(function*(){if(cc.isBrowser()){const{StorageManagerWeb:t}=yield Promise.resolve().then((function(){return Kd}));return new t(e)}const t={persistChannelConfig:()=>l(void 0,void 0,void 0,(function*(){})),getPersistedChannelConfig:()=>l(void 0,void 0,void 0,(function*(){})),persistAccounts:()=>l(void 0,void 0,void 0,(function*(){})),getCachedAccounts:()=>l(void 0,void 0,void 0,(function*(){return[]})),persistChainId:()=>l(void 0,void 0,void 0,(function*(){})),getCachedChainId:()=>l(void 0,void 0,void 0,(function*(){})),terminate:()=>l(void 0,void 0,void 0,(function*(){}))};return Promise.resolve(t)}));class lc extends Pa{constructor({connectionStream:e,shouldSendMetadata:t,autoRequestAccounts:n=!1}){super(e,{logger:console,maxEventListeners:100,shouldSendMetadata:t}),this.state={accounts:null,autoRequestAccounts:!1,providerStateRequested:!1,chainId:"",networkVersion:""},Ra(`[SDKProvider: constructor()] autoRequestAccounts=${n}`),this.state.autoRequestAccounts=n}forceInitializeState(){return l(this,void 0,void 0,(function*(){return Ra(`[SDKProvider: forceInitializeState()] autoRequestAccounts=${this.state.autoRequestAccounts}`),this._initializeStateAsync()}))}_setConnected(){Ra("[SDKProvider: _setConnected()] Setting connected state"),this._state.isConnected=!0}getState(){return this._state}getSDKProviderState(){return this.state}getSelectedAddress(){var e;const{accounts:t}=this._state;return t&&0!==t.length?(null===(e=t[0])||void 0===e?void 0:e.toLowerCase())||"":(Ra("[SDKProvider: getSelectedAddress] No accounts found"),null)}getChainId(){return this.state.chainId}getNetworkVersion(){return this.state.networkVersion}setSDKProviderState(e){this.state=Object.assign(Object.assign({},this.state),e)}handleAccountsChanged(e,t){return this._handleAccountsChanged(e,t)}handleDisconnect({terminate:e=!1}){!function({terminate:e=!1,instance:t}){const{state:n}=t;Ra(`[SDKProvider: handleDisconnect()] cleaning up provider state terminate=${e}`,t),e&&(t._state.accounts=null,t._state.isUnlocked=!1,t._state.isPermanentlyDisconnected=!0,t._state.initialized=!1),t._handleAccountsChanged([]),t._state.isConnected=!1,t.emit("disconnect",Oa.ethErrors.provider.disconnected()),n.providerStateRequested=!1}({terminate:e,instance:this})}_initializeStateAsync(){return l(this,void 0,void 0,(function*(){return function(e){var t,n;return l(this,void 0,void 0,(function*(){void 0===e.state&&(e.state={accounts:null,autoRequestAccounts:!1,providerStateRequested:!1,chainId:""});const{state:r}=e;let i;if(r.providerStateRequested)Ra("[SDKProvider: initializeStateAsync()] initialization already in progress");else{let o;r.providerStateRequested=!0;let s=null,a=!1,c=!1;const d=yield dc({enabled:!0});if(d){const e=yield d.getPersistedChannelConfig({});a=null!==(t=null==e?void 0:e.relayPersistence)&&void 0!==t&&t,o=yield d.getCachedChainId();const n=yield d.getCachedAccounts();n.length>0&&(s=n[0])}if(Ra(`[SDKProvider: initializeStateAsync()] relayPersistence=${a}`,{relayPersistence:a,cachedChainId:o,cachedSelectedAddress:s}),a)if(o&&s)i={accounts:[s],chainId:o,isUnlocked:!1},c=!0;else try{i=yield e.request({method:"metamask_getProviderState"})}catch(t){return e._log.error("MetaMask: Failed to get initial state. Please report this bug.",t),void(r.providerStateRequested=!1)}if(0===(null===(n=null==i?void 0:i.accounts)||void 0===n?void 0:n.length))if(e.getSelectedAddress())i.accounts=[e.getSelectedAddress()];else{Ra("[SDKProvider: initializeStateAsync()] Fetch accounts remotely.");const t=yield e.request({method:"eth_requestAccounts",params:[]});i.accounts=t}e._initializeState(i),r.providerStateRequested=!1,c&&(e._state.isConnected=!0,e.emit("connect",{chainId:null==i?void 0:i.chainId}))}}))}(this)}))}_initializeState(e){return Ra("[SDKProvider: _initializeState()]",e),function(e,t,n){return Ra("[SDKProvider: initializeState()] set state._initialized to false"),e._state.initialized=!1,t(n)}(this,super._initializeState.bind(this),e)}_handleChainChanged({chainId:e,networkVersion:t}={}){this.state.chainId=e,this.state.networkVersion=t,function({instance:e,chainId:t,networkVersion:n,superHandleChainChanged:r}){Ra(`[SDKProvider: handleChainChanged()] chainId=${t} networkVersion=${n}`);let i=n;n||(Ra("[SDKProvider: handleChainChanged()] forced network version to prevent provider error"),i="1"),e._state.isConnected=!0,e.emit("connect",{chainId:t}),r({chainId:t,networkVersion:i})}({instance:this,chainId:e,networkVersion:t,superHandleChainChanged:super._handleChainChanged.bind(this)})}}var uc,hc={name:"@metamask/sdk",version:"0.32.0",description:"",homepage:"https://github.com/MetaMask/metamask-sdk#readme",bugs:{url:"https://github.com/MetaMask/metamask-sdk/issues"},repository:{type:"git",url:"https://github.com/MetaMask/metamask-sdk",directory:"packages/sdk"},main:"dist/node/cjs/metamask-sdk.js",module:"dist/browser/es/metamask-sdk.js",browser:"dist/browser/es/metamask-sdk.js",unpkg:"dist/browser/umd/metamask-sdk.js","react-native":"dist/react-native/es/metamask-sdk.js",types:"dist/types/src/index.d.ts",sideEffects:!1,files:["/dist"],scripts:{"build:types":"tsc --project tsconfig.build.json --emitDeclarationOnly --outDir dist/types",build:"yarn build:types && rollup -c --bundleConfigAsCjs","build:clean":"yarn clean && yarn build","build:post-tsc":"echo 'N/A'","build:pre-tsc":"echo 'N/A'",typecheck:"tsc --noEmit",clean:"rimraf ./dist",size:"node bundle-size && size-limit",lint:"yarn lint:eslint && yarn lint:misc --check","lint:changelog":"../../scripts/validate-changelog.sh @metamask/sdk","lint:eslint":"eslint . --cache --ext js,ts","lint:fix":"yarn lint:eslint --fix && yarn lint:misc --write","lint:misc":"prettier '**/*.json' '**/*.md' '!CHANGELOG.md' --ignore-path ../../.gitignore",prepack:"../../scripts/prepack.sh","publish:preview":"yarn npm publish --tag preview",reset:"yarn clean && rimraf ./node_modules/",test:'jest --testPathIgnorePatterns "/e2e/"',"test:coverage":'jest --coverage --testPathIgnorePatterns "/e2e/"',"test:e2e":'jest --testPathPattern "/e2e/"',"test:ci":'jest --coverage --passWithNoTests --setupFilesAfterEnv ./jest-preload.js --testPathIgnorePatterns "/e2e/"',"test:dev":'jest -c ./jest.config.ts --detectOpenHandles  --testPathIgnorePatterns "/e2e/"',watch:"rollup -c -w",dev:'concurrently "tsc --watch" "rollup -c -w --bundleConfigAsCjs"',"build:dev":"yarn build:types && NODE_ENV=dev rollup -c --bundleConfigAsCjs"},dependencies:{"@babel/runtime":"^7.26.0","@metamask/onboarding":"^1.0.1","@metamask/providers":"16.1.0","@metamask/sdk-communication-layer":"workspace:*","@metamask/sdk-install-modal-web":"workspace:*","@paulmillr/qr":"^0.2.1",bowser:"^2.9.0","cross-fetch":"^4.0.0",debug:"^4.3.4",eciesjs:"^0.4.11","eth-rpc-errors":"^4.0.3",eventemitter2:"^6.4.9","obj-multiplex":"^1.0.0",pump:"^3.0.0","readable-stream":"^3.6.2","socket.io-client":"^4.5.1",tslib:"^2.6.0",util:"^0.12.4",uuid:"^8.3.2"},devDependencies:{"@jest/globals":"^29.3.1","@lavamoat/allow-scripts":"^2.3.1","@metamask/auto-changelog":"3.1.0","@metamask/eslint-config":"^6.0.0","@metamask/eslint-config-nodejs":"^6.0.0","@metamask/eslint-config-typescript":"^6.0.0","@react-native-async-storage/async-storage":"^1.19.6","@rollup/plugin-alias":"^5.1.1","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.0.2","@rollup/plugin-replace":"^6.0.1","@rollup/plugin-terser":"^0.4.1","@size-limit/preset-big-lib":"^11.0.2","@types/dom-screen-wake-lock":"^1.0.2","@types/node":"^20.1.3","@types/pump":"^1.1.1","@types/qrcode-terminal":"^0.12.0","@types/uuid":"^10.0.0","@typescript-eslint/eslint-plugin":"^4.26.0","@typescript-eslint/parser":"^4.26.0","browserify-zlib":"^0.2.0",buffer:"^6.0.3",concurrently:"^9.1.2","crypto-browserify":"^3.12.0",eslint:"^7.30.0","eslint-config-prettier":"^8.3.0","eslint-plugin-import":"^2.23.4","eslint-plugin-jest":"^24.4.0","eslint-plugin-jsdoc":"^36.1.0","eslint-plugin-node":"^11.1.0","eslint-plugin-prettier":"^3.4.0","https-browserify":"^1.0.0",jest:"^29.3.1","jest-environment-jsdom":"^29.3.1",prettier:"^2.3.0",process:"^0.11.10",rimraf:"^4.4.0",rollup:"^4.26.0","rollup-plugin-analyzer":"^4.0.0","rollup-plugin-jscc":"^2.0.0","rollup-plugin-natives":"^0.7.5","rollup-plugin-node-builtins":"^2.1.2","rollup-plugin-polyfill-node":"^0.13.0","rollup-plugin-sizes":"^1.0.6","rollup-plugin-typescript2":"^0.31.2","rollup-plugin-visualizer":"^5.12.0","size-limit":"^11.0.2","stream-browserify":"^3.0.0","stream-http":"^3.2.0","ts-jest":"^29.0.3","ts-node":"^10.9.1",typescript:"^4.3.2",url:"^0.11.0",webpack:"^5.0.0"},publishConfig:{access:"public",registry:"https://registry.npmjs.org/"},lavamoat:{allowScripts:{"eciesjs>secp256k1":!1,"socket.io-client>engine.io-client>ws>bufferutil":!1,"socket.io-client>engine.io-client>ws>utf-8-validate":!1,"@metamask/sdk-communication-layer>bufferutil":!1,"@metamask/sdk-communication-layer>eciesjs>secp256k1":!1,"@metamask/sdk-communication-layer>utf-8-validate":!1}}};!function(e){e.INPAGE="metamask-inpage",e.CONTENT_SCRIPT="metamask-contentscript",e.PROVIDER="metamask-provider"}(uc||(uc={}));const fc="direct",pc="https://metamask.app.link/connect",mc="metamask://connect",gc={NAME:"MetaMask",RDNS:["io.metamask","io.metamask.flask"]},vc=/(?:^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}$)|(?:^0{8}-0{4}-0{4}-0{4}-0{12}$)/u,yc={METAMASK_GETPROVIDERSTATE:"metamask_getProviderState",METAMASK_CONNECTSIGN:"metamask_connectSign",METAMASK_CONNECTWITH:"metamask_connectWith",METAMASK_OPEN:"metamask_open",METAMASK_BATCH:"metamask_batch",PERSONAL_SIGN:"personal_sign",WALLET_REQUESTPERMISSIONS:"wallet_requestPermissions",WALLET_REVOKEPERMISSIONS:"wallet_revokePermissions",WALLET_GETPERMISSIONS:"wallet_getPermissions",WALLET_WATCHASSET:"wallet_watchAsset",WALLET_ADDETHEREUMCHAIN:"wallet_addEthereumChain",WALLET_SWITCHETHETHEREUMCHAIN:"wallet_switchEthereumChain",ETH_REQUESTACCOUNTS:"eth_requestAccounts",ETH_ACCOUNTS:"eth_accounts",ETH_CHAINID:"eth_chainId",ETH_SENDTRANSACTION:"eth_sendTransaction",ETH_SIGNTYPEDDATA:"eth_signTypedData",ETH_SIGNTYPEDDATA_V3:"eth_signTypedData_v3",ETH_SIGNTYPEDDATA_V4:"eth_signTypedData_v4",ETH_SIGNTRANSACTION:"eth_signTransaction",ETH_SIGN:"eth_sign",PERSONAL_EC_RECOVER:"personal_ecRecover"},Ec={[yc.ETH_REQUESTACCOUNTS]:!0,[yc.ETH_SENDTRANSACTION]:!0,[yc.ETH_SIGNTRANSACTION]:!0,[yc.ETH_SIGN]:!0,[yc.PERSONAL_SIGN]:!0,[yc.ETH_ACCOUNTS]:!1,[yc.ETH_CHAINID]:!1,[yc.PERSONAL_SIGN]:!0,[yc.ETH_SIGNTYPEDDATA]:!0,[yc.ETH_SIGNTYPEDDATA_V3]:!0,[yc.ETH_SIGNTYPEDDATA_V4]:!0,[yc.WALLET_REQUESTPERMISSIONS]:!0,[yc.WALLET_GETPERMISSIONS]:!0,[yc.WALLET_WATCHASSET]:!0,[yc.WALLET_ADDETHEREUMCHAIN]:!0,[yc.WALLET_SWITCHETHETHEREUMCHAIN]:!0,[yc.METAMASK_CONNECTSIGN]:!0,[yc.METAMASK_CONNECTWITH]:!0,[yc.PERSONAL_EC_RECOVER]:!0,[yc.METAMASK_BATCH]:!0,[yc.METAMASK_OPEN]:!0},bc=Object.keys(Ec).map((e=>e.toLowerCase())),wc=["eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4","eth_sign"].map((e=>e.toLowerCase())),Sc=".sdk-comm",_c="providerType",Cc=".MMSDK_cached_address",xc=".MMSDK_cached_chainId",kc={CHAIN_CHANGED:"chainChanged",ACCOUNTS_CHANGED:"accountsChanged",DISCONNECT:"disconnect",CONNECT:"connect",CONNECTED:"connected"},Mc=1e6;var Ac;exports.PROVIDER_UPDATE_TYPE=void 0,(Ac=exports.PROVIDER_UPDATE_TYPE||(exports.PROVIDER_UPDATE_TYPE={})).TERMINATE="terminate",Ac.EXTENSION="extension",Ac.INITIALIZED="initialized";const Tc="undefined"!=typeof window&&window.localStorage;function Ic({instance:e,msg:t}){return l(this,void 0,void 0,(function*(){if(e._initialized||(Ra("[MetaMaskSDK: connectAndSign()] provider not ready -- wait for init()"),yield e.init()),Ra(`[MetaMaskSDK: connectAndSign()] activeProvider=${e.activeProvider}`),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");const n=/^0x([0-9A-Fa-f]{2})*$/u.test(t)?t:function(e){let t;if("undefined"!=typeof Buffer)t=Buffer.from(e,"utf8").toString("hex");else if("undefined"!=typeof TextEncoder){const n=(new TextEncoder).encode(e);t=Array.from(n).map((e=>e.toString(16).padStart(2,"0"))).join("")}else{if("object"!=typeof global||!("Buffer"in global))throw new Error("Unable to convert string to hex: No available method.");t=global.Buffer.from(e,"utf8").toString("hex")}return`0x${t}`}(t);return e.activeProvider.request({method:yc.METAMASK_CONNECTWITH,params:[{method:yc.PERSONAL_SIGN,params:[n]}]})}))}function Pc(e){var t,n;return l(this,void 0,void 0,(function*(){Ra("[MetaMaskSDK: connectWithExtensionProvider()] ",e),e.sdkProvider=e.activeProvider,e.activeProvider=window.extension,window.ethereum=window.extension;try{const e=yield null===(t=window.extension)||void 0===t?void 0:t.request({method:"eth_requestAccounts"});Ra(`[MetaMaskSDK: connectWithExtensionProvider()] accounts=${e}`)}catch(e){return void console.warn("[MetaMaskSDK: connectWithExtensionProvider()] can't request accounts error",e)}localStorage.setItem(_c,"extension"),e.extensionActive=!0,e.emit(sc.ProviderUpdate,exports.PROVIDER_UPDATE_TYPE.EXTENSION),e.options.enableAnalytics&&(null===(n=e.analytics)||void 0===n||n.send({event:lr.SDK_USE_EXTENSION}))}))}function Rc(e){let t;if("undefined"!=typeof Buffer)t=Buffer.from(e,"utf8").toString("base64");else if("function"==typeof btoa)t=btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/gu,((e,t)=>String.fromCharCode(parseInt(t,16)))));else{if("object"!=typeof global||!("Buffer"in global))throw new Error("Unable to base64 encode: No available method.");t=global.Buffer.from(e,"utf8").toString("base64")}return t}function Oc(e,t,r,i){var o,s,a,c,d,u,h,f,p,m,g,v,y,E,b,w,S,_,C,x;return l(this,void 0,void 0,(function*(){const r=null===(o=e.state.remote)||void 0===o?void 0:o.isReady(),l=null===(s=e.state.remote)||void 0===s?void 0:s.isConnected(),k=null===(a=e.state.remote)||void 0===a?void 0:a.isPaused(),M=ac.getProvider(),A=null===(c=e.state.remote)||void 0===c?void 0:c.getChannelId(),T=null===(d=e.state.remote)||void 0===d?void 0:d.isAuthorized(),{deeplinkProtocol:I}=e.state,{method:P,data:R,triggeredInstaller:O}=(e=>{var t,r,i,o;let s;n.Buffer.isBuffer(e)?(s=e.toJSON(),s._isBuffer=!0):s=e;const a=null===(t=null==s?void 0:s.data)||void 0===t?void 0:t.method;let c=!1;return"object"==typeof(null===(r=null==s?void 0:s.data)||void 0===r?void 0:r.params)&&!0===(null===(o=null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.params)||void 0===o?void 0:o.__triggeredInstaller)&&(c=!0,s.data.params=s.data.params.wrappedParams),{method:a,data:s,triggeredInstaller:c}})(t);if(Ra(`[RCPMS: write()] method='${P}' isRemoteReady=${r} channelId=${A} isSocketConnected=${l} isRemotePaused=${k} providerConnected=${M.isConnected()}`,t),!A)return P!==yc.METAMASK_GETPROVIDERSTATE&&Ra(`[RCPMS: write()] ${P} --\x3e channelId is undefined`),i(new Error("disconnected"));Ra(`[RCPMS: write()] remote.isPaused()=${null===(u=e.state.remote)||void 0===u?void 0:u.isPaused()} authorized=${T} ready=${r} socketConnected=${l}`,t);const N=null===(h=e.state.platformManager)||void 0===h?void 0:h.isSecure(),D=null!==(p=null===(f=e.state.platformManager)||void 0===f?void 0:f.isMobileWeb())&&void 0!==p&&p,L=null!==(g=null===(m=e.state.remote)||void 0===m?void 0:m.hasDeeplinkProtocol())&&void 0!==g&&g&&D&&T;try{if(!O){const t=JSON.stringify(null==R?void 0:R.data);if(t.length>Mc)return i(new Error(`Message size ${t.length} exceeds maximum allowed size of 1000000 bytes`));null===(v=e.state.remote)||void 0===v||v.sendMessage(null==R?void 0:R.data).then((()=>{Ra(`[RCPMS: _write()] ${P} sent successfully`)})).catch((e=>{Ra("[RCPMS: _write()] error sending message",e)}))}if(!N)return Ra(`[RCPMS: _write()] unsecure platform for method ${P} -- return callback`),i();if(O)return Ra("[RCPMS: _write()] prevent deeplink -- installation completed separately."),i();const t=null!==(b=null===(E=null===(y=e.state.remote)||void 0===y?void 0:y.getKeyInfo())||void 0===E?void 0:E.ecies.public)&&void 0!==b?b:"";let n=encodeURI(`channelId=${A}&pubkey=${t}&comm=socket&t=d&v=2`);if(L){const t=JSON.stringify(null==R?void 0:R.data),r=null===(w=e.state.remote)||void 0===w?void 0:w.encrypt(t);if(!r)return Ra("[RCPMS: _write()] error encrypting message"),i(new Error("RemoteCommunicationPostMessageStream - disconnected"));n+=`&scheme=${I}&rpc=${Rc(r)}`}if(!(null===(S=e.state.platformManager)||void 0===S?void 0:S.isMetaMaskInstalled()))return Ra("[RCPMS: _write()] prevent deeplink until installation is completed."),i();Ec[P]?(Ra(`[RCPMS: _write()] redirect link for '${P}' socketConnected=${l} connect?${n}`),null===(_=e.state.platformManager)||void 0===_||_.openDeeplink(`${pc}?${n}`,`${mc}?${n}`,"_self")):(null===(C=e.state.remote)||void 0===C?void 0:C.isPaused())?(Ra(`[RCPMS: _write()] MM is PAUSED! deeplink with connect! targetMethod=${P}`),null===(x=e.state.platformManager)||void 0===x||x.openDeeplink(`${pc}?redirect=true&${n}`,`${mc}?redirect=true&${n}`,"_self")):Ra(`[RCPMS: _write()] method ${P} doesn't need redirect.`)}catch(e){return Ra("[RCPMS: _write()] error sending message",e),i(new Error("RemoteCommunicationPostMessageStream - disconnected"))}return i()}))}class Nc extends ca.Duplex{constructor({name:e,remote:t,deeplinkProtocol:n,platformManager:r}){super({objectMode:!0}),this.state={_name:null,remote:null,deeplinkProtocol:!1,platformManager:null},this.state._name=e,this.state.remote=t,this.state.deeplinkProtocol=n,this.state.platformManager=r,this._onMessage=this._onMessage.bind(this),this.state.remote.on(exports.EventType.MESSAGE,this._onMessage)}_write(e,t,n){return l(this,void 0,void 0,(function*(){return Oc(this,e,0,n)}))}_read(){}_onMessage(e){return function(e,t){try{if(Ra("[RCPMS: onMessage()] message",t),!t||"object"!=typeof t)return;if("object"!=typeof(null==t?void 0:t.data))return;if(!(null==t?void 0:t.name))return void Ra("[RCPMS: onMessage()] ignore message without name",t);if((null==t?void 0:t.name)!==uc.PROVIDER)return void Ra(`[RCPMS: onMessage()] ignore message with wrong name message=${t}`);if(n.Buffer.isBuffer(t)){const r=n.Buffer.from(t);e.push(r)}else e.push(t)}catch(e){Ra(`[RCPMS: onMessage()] ignore message error err=${e}`)}}(this,e)}start(){}}let Dc=1;const Lc=e=>new Promise((t=>{setTimeout((()=>{t(!0)}),e)})),Bc=({checkInstallationOnAllCalls:t=!1,communicationLayerPreference:n,injectProvider:r,shouldShimWeb3:i,platformManager:o,installer:s,sdk:a,remoteConnection:c,debug:d})=>l(void 0,void 0,void 0,(function*(){var u,h;const f=(({name:e,remoteConnection:t})=>{if(!t||!(null==t?void 0:t.getConnector()))throw new Error("Missing remote connection parameter");return new Nc({name:e,remote:null==t?void 0:t.getConnector(),deeplinkProtocol:null==t?void 0:t.state.deeplinkProtocol,platformManager:null==t?void 0:t.getPlatformManager()})})({name:uc.INPAGE,target:uc.CONTENT_SCRIPT,platformManager:o,communicationLayerPreference:n,remoteConnection:c}),p=o.getPlatformType(),m=a.options.dappMetadata,g=`Sdk/Javascript SdkVersion/${hc.version} Platform/${p} dApp/${null!==(u=m.url)&&void 0!==u?u:m.name} dAppTitle/${m.name}`;let v=null,y=null;const E=null===(h=a.options.storage)||void 0===h?void 0:h.storageManager;if(E){try{const e=yield E.getCachedAccounts();e.length>0&&(v=e[0])}catch(e){console.error(`[initializeMobileProvider] failed to get cached addresses: ${e}`)}try{const e=yield E.getCachedChainId();e&&(y=e)}catch(e){console.error(`[initializeMobileProvider] failed to parse cached chainId: ${e}`)}}Ra(`[initializeMobileProvider] cachedAccountAddress: ${v}, cachedChainId: ${y}`);const b=!(!r||p===exports.PlatformType.NonBrowser||p===exports.PlatformType.ReactNative),w=ac.init({shouldSetOnWindow:b,connectionStream:f,shouldShimWeb3:i,sdkInstance:a});let S=!1;const _=e=>{S=e},C=()=>S,x=(n,r,i,d)=>l(void 0,void 0,void 0,(function*(){var u,h,f,p,m,b,w,x,k;const M=ac.getProvider();if(S){M.emit("display_uri",(null==c?void 0:c.state.qrcodeLink)||""),null==c||c.showActiveModal();let e=C();for(;e;){const t=C(),n=null==c?void 0:c.isAuthorized();e=t&&!n,Ra(`[initializeMobileProvider: sendRequest()] waiting for initialization to complete - initializing: ${t} authorized: ${n}`),yield Lc(1e3)}return Ra("[initializeMobileProvider: sendRequest()] initial method completed -- prevent installation and call provider"),i(...r)}const A=o.isMetaMaskInstalled(),T=null==c?void 0:c.isConnected();let I=null,P=null,R=null;if(I=null!==(u=M.getSelectedAddress())&&void 0!==u?u:v,R=M.getChainId()||y,I&&E&&I!==v&&E.persistAccounts([I]).catch((e=>{console.error(`[initializeMobileProvider] failed to persist account: ${e}`)})),R&&(y=R,E&&E.persistChainId(R).catch((e=>{console.error(`[initializeMobileProvider] failed to persist chainId: ${e}`)}))),Ra("[initializeMobileProvider: sendRequest()]",{selectedAddress:I,chainId:R}),d&&Ra(`[initializeMobileProvider: sendRequest()] method=${n} ongoing=${S} selectedAddress=${I} isInstalled=${A} checkInstallationOnAllCalls=${t} socketConnected=${T}`),I&&n.toLowerCase()===yc.ETH_ACCOUNTS.toLowerCase())return[I];if(R&&n.toLowerCase()===yc.ETH_CHAINID.toLowerCase())return R;const O=[yc.ETH_REQUESTACCOUNTS,yc.WALLET_REQUESTPERMISSIONS,yc.METAMASK_CONNECTSIGN,yc.METAMASK_CONNECTWITH],N=!Ec[n],D=null===(h=a.options.readonlyRPCMap)||void 0===h?void 0:h[R];if(D&&N)try{const t=null===(f=null==r?void 0:r[0])||void 0===f?void 0:f.params,i=yield(({rpcEndpoint:t,method:n,sdkInfo:r,params:i})=>l(void 0,void 0,void 0,(function*(){const o=JSON.stringify({jsonrpc:"2.0",method:n,params:i,id:(Dc+=1,Dc)}),s={Accept:"application/json","Content-Type":"application/json"};let a;t.includes("infura")&&(s["Metamask-Sdk-Info"]=r);try{a=yield e(t,{method:"POST",headers:s,body:o})}catch(e){throw e instanceof Error?new Error(`Failed to fetch from RPC: ${e.message}`):new Error(`Failed to fetch from RPC: ${e}`)}if(!a.ok)throw new Error(`Server responded with a status of ${a.status}`);return(yield a.json()).result})))({rpcEndpoint:D,sdkInfo:g,method:n,params:t||[]});return d&&Ra(`initializeProvider::ReadOnlyRPCResponse ${i}`),i}catch(e){console.warn(`[initializeMobileProvider: sendRequest()] method=${n} readOnlyRPCRequest failed:`,e)}if((!A||A&&!T)&&n!==yc.METAMASK_GETPROVIDERSTATE){const e=(null===(p=null==r?void 0:r[0])||void 0===p?void 0:p.params)||[];if(-1!==O.indexOf(n)||t){_(!0);const t=n===yc.METAMASK_CONNECTWITH,o=`${Date.now()}`;try{yield s.start({wait:!1,connectWith:t?{method:n,id:o,params:e}:void 0}),yield new Promise(((e,t)=>{(null==c?void 0:c.isAuthorized())&&(Ra("[initializeMobileProvider: sendRequest()] already authorized"),e(!0)),null==c||c.getConnector().once(exports.EventType.AUTHORIZED,(()=>{e(!0)})),a.once(exports.EventType.PROVIDER_UPDATE,(e=>{Ra(`[initializeMobileProvider: sendRequest()] PROVIDER_UPDATE --- remote provider request interupted type=${e}`),e===exports.PROVIDER_UPDATE_TYPE.EXTENSION?t(exports.EventType.PROVIDER_UPDATE):t(new Error("Connection Terminated"))}))}))}catch(t){if(exports.PROVIDER_UPDATE_TYPE.EXTENSION===t){if(Ra(`[initializeMobileProvider: sendRequest()] extension provider detect: re-create ${n} on the active provider`),n.toLowerCase()===yc.METAMASK_CONNECTSIGN.toLowerCase()){const t=yield null===(m=a.getProvider())||void 0===m?void 0:m.request({method:yc.ETH_REQUESTACCOUNTS,params:[]});if(!t.length)throw new Error("SDK state invalid -- undefined accounts");const n=yield null===(b=a.getProvider())||void 0===b?void 0:b.request({method:yc.PERSONAL_SIGN,params:[e[0],t[0]]});return a.emit(sc.ConnectWithResponse,n),n}if(n.toLowerCase()===yc.METAMASK_CONNECTWITH.toLowerCase()){const[t]=e,n=yield(({method:e,sdk:t,params:n})=>l(void 0,void 0,void 0,(function*(){var r,i,o,s;if(!t.isExtensionActive())throw new Error("SDK state invalid -- extension is not active");Ra("[MetaMaskProvider: extensionConnectWithOverwrite()] Overwriting request method",e,n);const a=yield null===(r=t.getProvider())||void 0===r?void 0:r.request({method:yc.ETH_REQUESTACCOUNTS,params:[]});if(!a.length)throw new Error("SDK state invalid -- undefined accounts");if((null==e?void 0:e.toLowerCase())===yc.PERSONAL_SIGN.toLowerCase()){const r={method:e,params:[n[0],a[0]]};return yield null===(i=t.getProvider())||void 0===i?void 0:i.request(r)}if((null==e?void 0:e.toLowerCase())===yc.ETH_SENDTRANSACTION.toLowerCase()){const r={method:e,params:[Object.assign(Object.assign({},n[0]),{from:a[0]})]};return yield null===(o=t.getProvider())||void 0===o?void 0:o.request(r)}return wc.includes(e.toLowerCase())?(console.warn(`MetaMaskSDK connectWith method=${e} -- not handled by the extension -- call separately`),a):yield null===(s=t.getProvider())||void 0===s?void 0:s.request({method:e,params:n})})))({method:t.method,sdk:a,params:t.params});return a.emit(sc.ConnectWithResponse,n),n}return Ra(`[initializeMobileProvider: sendRequest()] sending '${n}' on active provider`,e),yield null===(w=a.getProvider())||void 0===w?void 0:w.request({method:n,params:e})}if(t===exports.EventType.REJECTED)throw null==c||c.closeModal(),null===(x=a.getProvider())||void 0===x||x.handleDisconnect({terminate:!1}),Object.assign(new Error("User rejected connection"),{code:4001});throw Ra(`[initializeMobileProvider: sendRequest()] failed to start installer: ${t}`),t}finally{_(!1)}if(n===yc.ETH_REQUESTACCOUNTS)return P=yield new Promise((e=>{const t=setInterval((()=>{const{accounts:n}=M.getState();n&&(clearInterval(t),e(n))}),100)})),Ra(`[initializeMobileProvider: sendRequest()] selectedAddress: ${I} --- SKIP rpc call`),P;if(n===yc.METAMASK_CONNECTWITH)try{let e=0;const t=5,n=({resolve:n,reject:r})=>{e+=1;const i=null==c?void 0:c.getConnector().getRPCMethodTracker(),s=null==i?void 0:i[o];return Ra(`TRACKER: update method ${o}`,s),(null==s?void 0:s.result)?(Ra("[initializeMobileProvider: sendRequest()] found result",s.result),a.emit(sc.ConnectWithResponse,s.result),void n(s.result)):(null==s?void 0:s.error)?(Ra("[initializeMobileProvider: sendRequest()] found error",s.error),void r(s.error)):e>=t?(Ra("[initializeMobileProvider: sendRequest()] max message count reached without result"),void r(new Error("Max message count reached without result"))):void Ra("[initializeMobileProvider: sendRequest()] not found yet, need to wait for next update")};let r,i;const s=yield new Promise(((e,t)=>{const s=null==c?void 0:c.getConnector().getRPCMethodTracker();Ra(`TRACKER: method ${o}`,s),(null==s?void 0:s[o].result)?(Ra("[initializeMobileProvider: sendRequest()] found result",null==s?void 0:s[o].result),e(null==s?void 0:s[o].result)):(null==s?void 0:s[o].error)&&(Ra("[initializeMobileProvider: sendRequest()] found error",null==s?void 0:s[o].error),t(null==s?void 0:s[o].error)),i=()=>n({resolve:e,reject:t}),r=null==c?void 0:c.getConnector().on(exports.EventType.RPC_UPDATE,i)}));return i&&(null==r||r.off(exports.EventType.RPC_UPDATE,i)),Ra("TRACKER: result",s),s}catch(e){throw Ra("[initializeMobileProvider: sendRequest()] error:",e),e}r[0]&&"object"==typeof r[0]&&(r[0].params={__triggeredInstaller:!0,wrappedParams:r[0].params});return i(...r)}if(o.isSecure()&&Ec[n])return i(...r);if(a.isExtensionActive())return Ra(`[initializeMobileProvider: sendRequest()] EXTENSION active - redirect request '${n}' to it`,r,e),yield null===(k=a.getProvider())||void 0===k?void 0:k.request({method:n,params:e});throw Ra(`[initializeMobileProvider: sendRequest()] method=${n} --- skip --- not connected/installed`),new Error("MetaMask is not connected/installed, please call eth_requestAccounts to connect first.")}try{const e=yield i(...r);if(Ra(`[initializeMobileProvider: sendRequest()] method=${n} rpcResponse`,e),n===yc.WALLET_REQUESTPERMISSIONS){const t=e.reduce(((e,t)=>{var n;if("eth_accounts"===t.parentCapability){const r=null===(n=t.caveats.find((e=>"restrictReturnedAccounts"===e.type)))||void 0===n?void 0:n.value;r&&e.push(...r)}return e}),[]);Ra("[initializeMobileProvider: sendRequest()] accountsToPersist:",t),t.length>0&&(M.handleAccountsChanged(t,!1),null==E||E.persistAccounts(t))}return e}catch(e){throw console.error("[initializeMobileProvider: sendRequest()] error:",e),e}})),{request:k}=w;w.request=(...e)=>l(void 0,void 0,void 0,(function*(){return x(null==e?void 0:e[0].method,e,k,d)}));const{send:M}=w;return w.send=(...e)=>l(void 0,void 0,void 0,(function*(){return x(null==e?void 0:e[0],e,M,d)})),Ra("[initializeMobileProvider: sendRequest()] metamaskStream.start()"),f.start(),w}));function Kc(e){var t,n,r,i;return l(this,void 0,void 0,(function*(){const{options:o}=e,s={communicationLayerPreference:null!==(t=o.communicationLayerPreference)&&void 0!==t?t:exports.CommunicationLayerPreference.SOCKET,platformManager:e.platformManager,sdk:e,checkInstallationOnAllCalls:o.checkInstallationOnAllCalls,injectProvider:null===(n=o.injectProvider)||void 0===n||n,shouldShimWeb3:null===(r=o.shouldShimWeb3)||void 0===r||r,extensionOnly:null===(i=o.extensionOnly)||void 0===i||i,installer:e.installer,remoteConnection:e.remoteConnection,debug:e.debug},a=yield Bc(s);e.activeProvider=a,function(e){var t,n,r,i;null===(n=null===(t=e.remoteConnection)||void 0===t?void 0:t.getConnector())||void 0===n||n.on(sc.ConnectionStatus,(t=>{e.emit(sc.ConnectionStatus,t)})),null===(i=null===(r=e.remoteConnection)||void 0===r?void 0:r.getConnector())||void 0===i||i.on(sc.ServiceStatus,(t=>{e.emit(sc.ServiceStatus,t)}))}(e)}))}const jc="sdk";class $c{constructor({serverUrl:e,enabled:t,originatorInfo:n}){this.serverURL=nr,this.serverURL=e,this.originatorInfo=n,this.enabled=null==t||t}send({event:e,params:t}){if(!this.enabled)return;const n=Object.assign(Object.assign({id:jc,event:e,sdkVersion:hc.version},this.originatorInfo),{params:t});Ra(`[Analytics: send()] event: ${e}`,n),Xn(n,this.serverURL).catch((e=>{Ra(`[Analytics: send()] error: ${e}`)}))}}const Hc=()=>{if("undefined"==typeof document)return;let e;const t=document.getElementsByTagName("link");for(let n=0;n<t.length;n++)"icon"!==t[n].getAttribute("rel")&&"shortcut icon"!==t[n].getAttribute("rel")||(e=t[n].getAttribute("href"));return e},Uc=163400;function Fc(e){var t,n,r;const{dappMetadata:i}=e,s=function({url:e,name:t}){var n;const r=e+t,i=Rc(r);if(!localStorage)return"";let s=null!==(n=localStorage.getItem(i))&&void 0!==n?n:"";if(!s){s=o.v4();try{localStorage.setItem(i,s)}catch(e){return""}}return s}({url:null!==(t=null==i?void 0:i.url)&&void 0!==t?t:"no_url",name:null!==(n=null==i?void 0:i.name)&&void 0!==n?n:"no_name"}),a=null===(r=e.platformManager)||void 0===r?void 0:r.getPlatformType(),c=a===exports.PlatformType.DesktopWeb,d=a===exports.PlatformType.MetaMaskMobileWebview;let l="N/A";return c?l="extension":d&&(l="mobile"),{id:s,from:l}}const qc=({provider:e,sdkInstance:t})=>{if("state"in e)throw new Error("INVALID EXTENSION PROVIDER");return new Proxy(e,{get:(n,r)=>"request"===r?function(e){var r,i;return l(this,void 0,void 0,(function*(){Ra("[wrapExtensionProvider()] Overwriting request method",e);const{method:o,params:s}=e,a=bc.includes(o.toLowerCase()),{id:c,from:d}=Fc(t);if(a&&(null===(r=t.analytics)||void 0===r||r.send({event:lr.SDK_RPC_REQUEST,params:{method:o,from:d,id:c}})),o===yc.METAMASK_BATCH&&Array.isArray(s))return(({target:e,args:t,trackEvent:n,sdkInstance:r})=>l(void 0,void 0,void 0,(function*(){var i,o;if("metamask_batch"!==t.method)throw new Error("Invalid usage");const s=[],a=null!==(i=null==t?void 0:t.params)&&void 0!==i?i:[];for(const t of a){const n=yield null==e?void 0:e.request({method:t.method,params:t.params});s.push(n)}const{id:c,from:d}=Fc(r);return n&&(null===(o=r.analytics)||void 0===o||o.send({event:lr.SDK_RPC_REQUEST_DONE,params:{method:t.method,from:d,id:c}})),s})))({target:n,args:e,trackEvent:a,sdkInstance:t});if(o.toLowerCase()===yc.METAMASK_CONNECTSIGN.toLowerCase()&&Array.isArray(s))return(({target:e,params:t})=>l(void 0,void 0,void 0,(function*(){const n=yield e.request({method:yc.ETH_REQUESTACCOUNTS,params:[]});if(!n.length)throw new Error("SDK state invalid -- undefined accounts");return yield e.request({method:yc.PERSONAL_SIGN,params:[t[0],n[0]]})})))({target:n,params:s});if(o.toLowerCase()===yc.METAMASK_CONNECTWITH.toLowerCase()&&Array.isArray(s))return(({target:e,params:t})=>l(void 0,void 0,void 0,(function*(){const[n]=t,r=n.method,i=n.params,o=yield e.request({method:yc.ETH_REQUESTACCOUNTS,params:[]});if(!o.length)throw new Error("SDK state invalid -- undefined accounts");return(null==r?void 0:r.toLowerCase())===yc.PERSONAL_SIGN.toLowerCase()?yield e.request({method:r,params:[i[0],o[0]]}):(null==r?void 0:r.toLowerCase())===yc.ETH_SENDTRANSACTION.toLowerCase()?yield e.request({method:r,params:[Object.assign(Object.assign({},i[0]),{from:o[0]})]}):wc.includes(r.toLowerCase())?(console.warn(`MetaMaskSDK connectWith method=${r} -- not handled by the extension -- call separately`),o):yield e.request({method:r,params:i})})))({target:n,params:s});let u;try{return u=yield n.request(e),u}finally{a&&(null===(i=t.analytics)||void 0===i||i.send({event:lr.SDK_RPC_REQUEST_DONE,params:{method:o,from:d,id:c}}))}}))}:"getChainId"===r?function(){return e.chainId}:"getNetworkVersion"===r?function(){return e.networkVersion}:"getSelectedAddress"===r?function(){return e.selectedAddress}:"isConnected"===r?function(){return e._state.isConnected}:n[r]})};var zc;function Wc({mustBeMetaMask:e,sdkInstance:t}){return l(this,void 0,void 0,(function*(){if("undefined"==typeof window)throw new Error("window not available");try{const e=yield new Promise(((e,t)=>{const n=setTimeout((()=>{t(new Error("eip6963RequestProvider timed out"))}),500);window.addEventListener(zc.Announce,(t=>{const r=t,{detail:{info:i,provider:o}={}}=r,{name:s,rdns:a,uuid:c}=null!=i?i:{};vc.test(c)&&s.startsWith(gc.NAME)&&gc.RDNS.includes(a)&&(clearTimeout(n),e(o))})),window.dispatchEvent(new Event(zc.Request))}));return qc({provider:e,sdkInstance:t})}catch(n){if(!e&&window.ethereum)return qc({provider:window.ethereum,sdkInstance:t});throw new Error("Provider not found")}}))}!function(e){e.Announce="eip6963:announceProvider",e.Request="eip6963:requestProvider"}(zc||(zc={}));const Vc=e=>l(void 0,void 0,void 0,(function*(){const{options:t}=e,{infuraAPIKey:n}=t;if(!n)return;const r={"0x1":`https://mainnet.infura.io/v3/${n}`,"0x5":`https://goerli.infura.io/v3/${n}`,"0xaa36a7":`https://sepolia.infura.io/v3/${n}`,"0xe708":`https://linea-mainnet.infura.io/v3/${n}`,"0xe704":`https://linea-goerli.infura.io/v3/${n}`,"0x89":`https://polygon-mainnet.infura.io/v3/${n}`,"0x13881":`https://polygon-mumbai.infura.io/v3/${n}`,"0x45":`https://optimism-mainnet.infura.io/v3/${n}`,"0x1a4":`https://optimism-goerli.infura.io/v3/${n}`,"0xa4b1":`https://arbitrum-mainnet.infura.io/v3/${n}`,"0x66eed":`https://arbitrum-goerli.infura.io/v3/${n}`,"0x2a15c308d":`https://palm-mainnet.infura.io/v3/${n}`,"0x2a15c3083":`https://palm-testnet.infura.io/v3/${n}`,"0xa86a":`https://avalanche-mainnet.infura.io/v3/${n}`,"0xa869":`https://avalanche-fuji.infura.io/v3/${n}`,"0x4e454152":`https://aurora-mainnet.infura.io/v3/${n}`,"0x4e454153":`https://aurora-testnet.infura.io/v3/${n}`,"0x534e5f4d41494e":`https://starknet-mainnet.infura.io/v3/${n}`,"0x534e5f474f45524c49":`https://starknet-goerli.infura.io/v3/${n}`,"0x534e5f474f45524c4932":`https://starknet-goerli2.infura.io/v3/${n}`,"0xa4ec":`https://celo-mainnet.infura.io/v3/${n}`,"0xaef3":`https://celo-alfajores.infura.io/v3/${n}`};e.options.readonlyRPCMap?e.options.readonlyRPCMap=Object.assign(Object.assign({},e.options.readonlyRPCMap),r):e.options.readonlyRPCMap=r}));const Gc=e=>l(void 0,void 0,void 0,(function*(){const{options:t}=e,{readonlyRPCMap:n}=t;if(n)try{Ra("[MetaMaskSDK: setupReadOnlyRPCProviders()] Setting up Readonly RPC Providers",n),e.setReadOnlyRPCCalls(!0)}catch(e){throw new Error("Invalid Infura Settings")}}));function Yc(e,t,n,r){return new(n||(n=Promise))((function(t,i){function o(e){try{a(r.next(e))}catch(e){i(e)}}function s(e){try{a(r.throw(e))}catch(e){i(e)}}function a(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,s)}a((r=r.apply(e,[])).next())}))}function Zc(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var Jc="INSTALLED",Qc="NOT_INSTALLED",Xc="REGISTERED",ed="REGISTERING",td="RELOADING",nd={CHROME:"https://chrome.google.com/webstore/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn",FIREFOX:"https://addons.mozilla.org/firefox/addon/ether-metamask/",DEFAULT:"https://metamask.io"},rd="REGISTRATION_IN_PROGRESS",id="FORWARDER_ID",od=function(){function e(t){var n=void 0===t?{}:t,r=n.forwarderOrigin,i=void 0===r?"https://fwd.metamask.io":r,o=n.forwarderMode,s=void 0===o?e.FORWARDER_MODE.INJECT:o;this.forwarderOrigin=i,this.forwarderMode=s,this.state=e.isMetaMaskInstalled()?Jc:Qc;var a=e._detectBrowser();this.downloadUrl=a?nd[a]:nd.DEFAULT,this._onMessage=this._onMessage.bind(this),this._onMessageFromForwarder=this._onMessageFromForwarder.bind(this),this._openForwarder=this._openForwarder.bind(this),this._openDownloadPage=this._openDownloadPage.bind(this),this.startOnboarding=this.startOnboarding.bind(this),this.stopOnboarding=this.stopOnboarding.bind(this),window.addEventListener("message",this._onMessage),s===e.FORWARDER_MODE.INJECT&&"true"===sessionStorage.getItem(rd)&&e._injectForwarder(this.forwarderOrigin)}return e.prototype._onMessage=function(e){if(e.origin===this.forwarderOrigin)return"metamask:reload"===e.data.type?this._onMessageFromForwarder(e):void console.debug("Unknown message from '"+e.origin+"' with data "+JSON.stringify(e.data))},e.prototype._onMessageUnknownStateError=function(e){throw new Error("Unknown state: '"+e+"'")},e.prototype._onMessageFromForwarder=function(t){return Yc(this,0,void 0,(function(){return Zc(this,(function(n){switch(n.label){case 0:switch(this.state){case td:return[3,1];case Qc:return[3,2];case Jc:return[3,3];case ed:return[3,5];case Xc:return[3,6]}return[3,7];case 1:return console.debug("Ignoring message while reloading"),[3,8];case 2:return console.debug("Reloading now to register with MetaMask"),this.state=td,location.reload(),[3,8];case 3:return console.debug("Registering with MetaMask"),this.state=ed,[4,e._register()];case 4:return n.sent(),this.state=Xc,t.source.postMessage({type:"metamask:registrationCompleted"},t.origin),this.stopOnboarding(),[3,8];case 5:return console.debug("Already registering - ignoring reload message"),[3,8];case 6:return console.debug("Already registered - ignoring reload message"),[3,8];case 7:this._onMessageUnknownStateError(this.state),n.label=8;case 8:return[2]}}))}))},e.prototype.startOnboarding=function(){sessionStorage.setItem(rd,"true"),this._openDownloadPage(),this._openForwarder()},e.prototype.stopOnboarding=function(){"true"===sessionStorage.getItem(rd)&&(this.forwarderMode===e.FORWARDER_MODE.INJECT&&(console.debug("Removing forwarder"),e._removeForwarder()),sessionStorage.setItem(rd,"false"))},e.prototype._openForwarder=function(){this.forwarderMode===e.FORWARDER_MODE.OPEN_TAB?window.open(this.forwarderOrigin,"_blank"):e._injectForwarder(this.forwarderOrigin)},e.prototype._openDownloadPage=function(){window.open(this.downloadUrl,"_blank")},e.isMetaMaskInstalled=function(){return Boolean(window.ethereum&&window.ethereum.isMetaMask)},e._register=function(){return window.ethereum.request({method:"wallet_registerOnboarding"})},e._injectForwarder=function(e){var t=document.body,n=document.createElement("iframe");n.setAttribute("height","0"),n.setAttribute("width","0"),n.setAttribute("style","display: none;"),n.setAttribute("src",e),n.setAttribute("id",id),t.insertBefore(n,t.children[0])},e._removeForwarder=function(){var e;null===(e=document.getElementById(id))||void 0===e||e.remove()},e._detectBrowser=function(){var e=oc.parse(window.navigator.userAgent);return"Firefox"===e.browser.name?"FIREFOX":["Chrome","Chromium"].includes(e.browser.name||"")?"CHROME":null},e.FORWARDER_MODE={INJECT:"INJECT",OPEN_TAB:"OPEN_TAB"},e}();function sd(e,{wait:t=!1}){return l(this,void 0,void 0,(function*(){return Ra(`[MetamaskInstaller: startInstaller()] wait=${t}`),t&&(yield Lc(1e3)),yield e.checkInstallation()}))}class ad{constructor({remote:e,preferDesktop:t,platformManager:n,debug:r=!1}){this.state={isInstalling:!1,hasInstalled:!1,resendRequest:null,preferDesktop:!1,platformManager:null,remote:null,debug:!1,connectWith:void 0},this.state.remote=e,this.state.preferDesktop=t,this.state.platformManager=n,this.state.debug=r}startDesktopOnboarding(){return function(){return l(this,void 0,void 0,(function*(){Ra("[MetamaskInstaller: startDesktopOnboarding() starting desktop onboarding"),window.ethereum&&(window.ethereum=void 0),(new od).startOnboarding()}))}()}redirectToProperInstall(){return l(this,void 0,void 0,(function*(){return function(e){var t,n;return l(this,void 0,void 0,(function*(){const{state:r}=e,i=null===(t=r.platformManager)||void 0===t?void 0:t.getPlatformType();if(Ra(`[MetamaskInstaller: redirectToProperInstall()] platform=${i}`),i===exports.PlatformType.MetaMaskMobileWebview)return!1;r.isInstalling=!0;try{yield null===(n=r.remote)||void 0===n?void 0:n.startConnection({connectWith:r.connectWith}),r.isInstalling=!1,r.hasInstalled=!0}catch(e){throw r.isInstalling=!1,e}return!0}))}(this)}))}checkInstallation(){return l(this,void 0,void 0,(function*(){return function(e){var t;return l(this,void 0,void 0,(function*(){const{state:n}=e,r=null===(t=n.platformManager)||void 0===t?void 0:t.isMetaMaskInstalled();return Ra(`[MetamaskInstaller: checkInstallation()] isInstalled=${r}`),!!r||(yield e.redirectToProperInstall())}))}(this)}))}start({wait:e=!1,connectWith:t}){return l(this,void 0,void 0,(function*(){this.state.connectWith=t,Ra(`[MetaMaskInstaller: start()] wait=${e}`,t),yield sd(this,{wait:e})}))}}function cd(e,t){return e.toString(2).padStart(t,"0")}function dd(e,t){const n=e%t;return n>=0?n:t+n}function ld(e,t){return new Array(e).fill(t)}function ud(...e){let t=0;for(const n of e)t=Math.max(t,n.length);const n=[];for(let r=0;r<t;r++)for(const t of e)r>=t.length||n.push(t[r]);return new Uint8Array(n)}function hd(e,t,n){if(n<0||n+t.length>e.length)return!1;for(let r=0;r<t.length;r++)if(t[r]!==e[n+r])return!1;return!0}function fd(e){return{has:t=>e.includes(t),decode:t=>{if(!Array.isArray(t)||t.length&&"string"!=typeof t[0])throw new Error("alphabet.decode input should be array of strings");return t.map((t=>{if("string"!=typeof t)throw new Error(`alphabet.decode: not string element=${t}`);const n=e.indexOf(t);if(-1===n)throw new Error(`Unknown letter: "${t}". Allowed: ${e}`);return n}))},encode:t=>{if(!Array.isArray(t)||t.length&&"number"!=typeof t[0])throw new Error("alphabet.encode input should be an array of numbers");return t.map((t=>{if(function(e){if(!Number.isSafeInteger(e))throw new Error(`Wrong integer: ${e}`)}(t),t<0||t>=e.length)throw new Error(`Digit index outside alphabet: ${t} (alphabet: ${e.length})`);return e[t]}))}}}class pd{static size(e,t){if("number"==typeof e&&(e={height:e,width:e}),!Number.isSafeInteger(e.height)&&e.height!==1/0)throw new Error(`Bitmap: wrong height=${e.height} (${typeof e.height})`);if(!Number.isSafeInteger(e.width)&&e.width!==1/0)throw new Error(`Bitmap: wrong width=${e.width} (${typeof e.width})`);return void 0!==t&&(e={width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}),e}static fromString(e){const t=(e=e.replace(/^\n+/g,"").replace(/\n+$/g,"")).split("\n"),n=t.length,r=new Array(n);let i;for(const e of t){const t=e.split("").map((e=>{if("X"===e)return!0;if(" "===e)return!1;if("?"!==e)throw new Error(`Bitmap.fromString: unknown symbol=${e}`)}));if(i&&t.length!==i)throw new Error(`Bitmap.fromString different row sizes: width=${i} cur=${t.length}`);i=t.length,r.push(t)}return i||(i=0),new pd({height:n,width:i},r)}constructor(e,t){const{height:n,width:r}=pd.size(e);this.data=t||Array.from({length:n},(()=>ld(r,void 0))),this.height=n,this.width=r}point(e){return this.data[e.y][e.x]}isInside(e){return 0<=e.x&&e.x<this.width&&0<=e.y&&e.y<this.height}size(e){if(!e)return{height:this.height,width:this.width};const{x:t,y:n}=this.xy(e);return{height:this.height-n,width:this.width-t}}xy(e){if("number"==typeof e&&(e={x:e,y:e}),!Number.isSafeInteger(e.x))throw new Error(`Bitmap: wrong x=${e.x}`);if(!Number.isSafeInteger(e.y))throw new Error(`Bitmap: wrong y=${e.y}`);return e.x=dd(e.x,this.width),e.y=dd(e.y,this.height),e}rect(e,t,n){const{x:r,y:i}=this.xy(e),{height:o,width:s}=pd.size(t,this.size({x:r,y:i}));for(let e=0;e<o;e++)for(let t=0;t<s;t++)this.data[i+e][r+t]="function"==typeof n?n({x:t,y:e},this.data[i+e][r+t]):n;return this}rectRead(e,t,n){return this.rect(e,t,((e,t)=>(n(e,t),t)))}hLine(e,t,n){return this.rect(e,{width:t,height:1},n)}vLine(e,t,n){return this.rect(e,{width:1,height:t},n)}border(e=2,t){const n=this.height+2*e,r=this.width+2*e,i=ld(e,t),o=Array.from({length:e},(()=>ld(r,t)));return new pd({height:n,width:r},[...o,...this.data.map((e=>[...i,...e,...i])),...o])}embed(e,t){return this.rect(e,t.size(),(({x:e,y:n})=>t.data[n][e]))}rectSlice(e,t=this.size()){const n=new pd(pd.size(t,this.size(this.xy(e))));return this.rect(e,t,(({x:e,y:t},r)=>n.data[t][e]=r)),n}inverse(){const{height:e,width:t}=this;return new pd({height:t,width:e}).rect({x:0,y:0},1/0,(({x:e,y:t})=>this.data[e][t]))}scale(e){if(!Number.isSafeInteger(e)||e>1024)throw new Error(`Wrong scale factor: ${e}`);const{height:t,width:n}=this;return new pd({height:e*t,width:e*n}).rect({x:0,y:0},1/0,(({x:t,y:n})=>this.data[Math.floor(n/e)][Math.floor(t/e)]))}clone(){return new pd(this.size()).rect({x:0,y:0},this.size(),(({x:e,y:t})=>this.data[t][e]))}assertDrawn(){this.rectRead(0,1/0,((e,t)=>{if("boolean"!=typeof t)throw new Error("Invalid color type="+typeof t)}))}toString(){return this.data.map((e=>e.map((e=>void 0===e?"?":e?"X":" ")).join(""))).join("\n")}toASCII(){const{height:e,width:t,data:n}=this;let r="";for(let i=0;i<e;i+=2){for(let o=0;o<t;o++){const t=n[i][o],s=i+1>=e||n[i+1][o];t||s?!t&&s?r+="▀":t&&!s?r+="▄":t&&s&&(r+=" "):r+="█"}r+="\n"}return r}toTerm(){const e="[0m",t=`[1;47m  ${e}`,n=`[40m  ${e}`;return this.data.map((e=>e.map((e=>e?n:t)).join(""))).join("\n")}toSVG(){let e=`<svg xmlns:svg="http://www.w3.org/2000/svg" viewBox="0 0 ${this.width} ${this.height}" version="1.1" xmlns="http://www.w3.org/2000/svg">`;return this.rectRead(0,1/0,(({x:t,y:n},r)=>{r&&(e+=`<rect x="${t}" y="${n}" width="1" height="1" />`)})),e+="</svg>",e}toGIF(){const e=e=>[255&e,e>>>8&255],t=[...e(this.width),...e(this.height)],n=[];this.rectRead(0,1/0,((e,t)=>n.push(+(!0===t))));const r=126,i=[71,73,70,56,55,97,...t,246,0,0,255,255,255,...ld(381,0),44,0,0,0,0,...t,0,7],o=Math.floor(n.length/r);for(let e=0;e<o;e++)i.push(127,128,...n.slice(r*e,r*(e+1)).map((e=>+e)));return i.push(n.length%r+1,128,...n.slice(o*r).map((e=>+e))),i.push(1,129,0,59),new Uint8Array(i)}toImage(e=!1){const{height:t,width:n}=this.size(),r=new Uint8Array(t*n*(e?3:4));let i=0;for(let o=0;o<t;o++)for(let t=0;t<n;t++){const n=this.data[o][t]?0:255;r[i++]=n,r[i++]=n,r[i++]=n,e||(r[i++]=255)}return{height:t,width:n,data:r}}}const md=["low","medium","quartile","high"],gd=["numeric","alphanumeric","byte","kanji","eci"],vd=[26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706],yd={low:[7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],medium:[10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],quartile:[13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],high:[17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]},Ed={low:[1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],medium:[1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],quartile:[1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],high:[1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]},bd={size:{encode:e=>21+4*(e-1),decode:e=>(e-17)/4},sizeType:e=>Math.floor((e+7)/17),alignmentPatterns(e){if(1===e)return[];const t=bd.size.encode(e)-6-1,n=t-6,r=Math.ceil(n/28);let i=Math.floor(n/r);i%2?i+=1:n%r*2>=r&&(i+=2);const o=[6];for(let e=1;e<r;e++)o.push(t-(r-e)*i);return o.push(t),o},ECCode:{low:1,medium:0,quartile:3,high:2},formatMask:21522,formatBits(e,t){const n=bd.ECCode[e]<<3|t;let r=n;for(let e=0;e<10;e++)r=r<<1^1335*(r>>9);return(n<<10|r)^bd.formatMask},versionBits(e){let t=e;for(let e=0;e<12;e++)t=t<<1^7973*(t>>11);return e<<12|t},alphabet:{numeric:fd("0123456789"),alphanumerc:fd("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:")},lengthBits:(e,t)=>({numeric:[10,12,14],alphanumeric:[9,11,13],byte:[8,16,16],kanji:[8,10,12],eci:[0,0,0]}[t][bd.sizeType(e)]),modeBits:{numeric:"0001",alphanumeric:"0010",byte:"0100",kanji:"1000",eci:"0111"},capacity(e,t){const n=vd[e-1],r=yd[t][e-1],i=Ed[t][e-1],o=Math.floor(n/i)-r,s=i-n%i;return{words:r,numBlocks:i,shortBlocks:s,blockLen:o,capacity:8*(n-r*i),total:(r+o)*i+i-s}}},wd=[(e,t)=>(e+t)%2==0,(e,t)=>t%2==0,(e,t)=>e%3==0,(e,t)=>(e+t)%3==0,(e,t)=>(Math.floor(t/2)+Math.floor(e/3))%2==0,(e,t)=>e*t%2+e*t%3==0,(e,t)=>(e*t%2+e*t%3)%2==0,(e,t)=>((e+t)%2+e*t%3)%2==0],Sd={tables:(e=>{const t=ld(256,0),n=ld(256,0);for(let e=0,r=1;e<256;e++)t[e]=r,n[r]=e,r<<=1,256&r&&(r^=285);return{exp:t,log:n}})(),exp:e=>Sd.tables.exp[e],log(e){if(0===e)throw new Error(`GF.log: wrong arg=${e}`);return Sd.tables.log[e]%255},mul:(e,t)=>0===e||0===t?0:Sd.tables.exp[(Sd.tables.log[e]+Sd.tables.log[t])%255],add:(e,t)=>e^t,pow:(e,t)=>Sd.tables.exp[Sd.tables.log[e]*t%255],inv(e){if(0===e)throw new Error(`GF.inverse: wrong arg=${e}`);return Sd.tables.exp[255-Sd.tables.log[e]]},polynomial(e){if(0==e.length)throw new Error("GF.polymomial: wrong length");if(0!==e[0])return e;let t=0;for(;t<e.length-1&&0==e[t];t++);return e.slice(t)},monomial(e,t){if(e<0)throw new Error(`GF.monomial: wrong degree=${e}`);if(0==t)return[0];let n=ld(e+1,0);return n[0]=t,Sd.polynomial(n)},degree:e=>e.length-1,coefficient:(e,t)=>e[Sd.degree(e)-t],mulPoly(e,t){if(0===e[0]||0===t[0])return[0];const n=ld(e.length+t.length-1,0);for(let r=0;r<e.length;r++)for(let i=0;i<t.length;i++)n[r+i]=Sd.add(n[r+i],Sd.mul(e[r],t[i]));return Sd.polynomial(n)},mulPolyScalar(e,t){if(0==t)return[0];if(1==t)return e;const n=ld(e.length,0);for(let r=0;r<e.length;r++)n[r]=Sd.mul(e[r],t);return Sd.polynomial(n)},mulPolyMonomial(e,t,n){if(t<0)throw new Error("GF.mulPolyMonomial: wrong degree");if(0==n)return[0];const r=ld(e.length+t,0);for(let t=0;t<e.length;t++)r[t]=Sd.mul(e[t],n);return Sd.polynomial(r)},addPoly(e,t){if(0===e[0])return t;if(0===t[0])return e;let n=e,r=t;n.length>r.length&&([n,r]=[r,n]);let i=ld(r.length,0),o=r.length-n.length,s=r.slice(0,o);for(let e=0;e<s.length;e++)i[e]=s[e];for(let e=o;e<r.length;e++)i[e]=Sd.add(n[e-o],r[e]);return Sd.polynomial(i)},remainderPoly(e,t){const n=Array.from(e);for(let r=0;r<e.length-t.length+1;r++){const e=n[r];if(0!==e)for(let i=1;i<t.length;i++)0!==t[i]&&(n[r+i]=Sd.add(n[r+i],Sd.mul(t[i],e)))}return n.slice(e.length-t.length+1,n.length)},divisorPoly(e){let t=[1];for(let n=0;n<e;n++)t=Sd.mulPoly(t,[1,Sd.pow(2,n)]);return t},evalPoly(e,t){if(0==t)return Sd.coefficient(e,0);let n=e[0];for(let r=1;r<e.length;r++)n=Sd.add(Sd.mul(t,n),e[r]);return n},euclidian(e,t,n){Sd.degree(e)<Sd.degree(t)&&([e,t]=[t,e]);let r=e,i=t,o=[0],s=[1];for(;2*Sd.degree(i)>=n;){let e=r,t=o;if(r=i,o=s,0===r[0])throw new Error("rLast[0] === 0");i=e;let n=[0];const a=Sd.inv(r[0]);for(;Sd.degree(i)>=Sd.degree(r)&&0!==i[0];){const e=Sd.degree(i)-Sd.degree(r),t=Sd.mul(i[0],a);n=Sd.addPoly(n,Sd.monomial(e,t)),i=Sd.addPoly(i,Sd.mulPolyMonomial(r,e,t))}if(n=Sd.mulPoly(n,o),s=Sd.addPoly(n,t),Sd.degree(i)>=Sd.degree(r))throw new Error(`Division failed r: ${i}, rLast: ${r}`)}const a=Sd.coefficient(s,0);if(0==a)throw new Error("sigmaTilde(0) was zero");const c=Sd.inv(a);return[Sd.mulPolyScalar(s,c),Sd.mulPolyScalar(i,c)]}};function _d(e,t){const{words:n,shortBlocks:r,numBlocks:i,blockLen:o,total:s}=bd.capacity(e,t),a=(c=n,{encode(e){const t=Sd.divisorPoly(c),n=Array.from(e);return n.push(...t.slice(0,-1).fill(0)),Uint8Array.from(Sd.remainderPoly(n,t))},decode(e){const t=e.slice(),n=Sd.polynomial(Array.from(e));let r=ld(c,0),i=!1;for(let e=0;e<c;e++){const t=Sd.evalPoly(n,Sd.exp(e));r[r.length-1-e]=t,0!==t&&(i=!0)}if(!i)return t;r=Sd.polynomial(r);const o=Sd.monomial(c,1),[s,a]=Sd.euclidian(o,r,c),d=ld(Sd.degree(s),0);let l=0;for(let e=1;e<256&&l<d.length;e++)0===Sd.evalPoly(s,e)&&(d[l++]=Sd.inv(e));if(l!==d.length)throw new Error("RS.decode: wrong errors number");for(let e=0;e<d.length;e++){const n=t.length-1-Sd.log(d[e]);if(n<0)throw new Error("RS.decode: wrong error location");const r=Sd.inv(d[e]);let i=1;for(let t=0;t<d.length;t++)e!==t&&(i=Sd.mul(i,Sd.add(1,Sd.mul(d[t],r))));t[n]=Sd.add(t[n],Sd.mul(Sd.evalPoly(a,r),Sd.inv(i)))}return t}});var c;return{encode(e){const t=[],n=[];for(let s=0;s<i;s++){const i=o+(s<r?0:1);t.push(e.subarray(0,i)),n.push(a.encode(e.subarray(0,i))),e=e.subarray(i)}const s=ud(...t),c=ud(...n),d=new Uint8Array(s.length+c.length);return d.set(s),d.set(c,s.length),d},decode(e){if(e.length!==s)throw new Error(`interleave.decode: len(data)=${e.length}, total=${s}`);const t=[];for(let e=0;e<i;e++){const i=e<r;t.push(new Uint8Array(n+o+(i?0:1)))}let c=0;for(let n=0;n<o;n++)for(let r=0;r<i;r++)t[r][n]=e[c++];for(let n=r;n<i;n++)t[n][o]=e[c++];for(let s=o;s<o+n;s++)for(let n=0;n<i;n++){const i=n<r;t[n][s+(i?0:1)]=e[c++]}const d=[];for(const e of t)d.push(...Array.from(a.decode(e)).slice(0,-n));return Uint8Array.from(d)}}}function Cd(e,t,n,r){let i="",o=n.length;if("numeric"===r){const e=bd.alphabet.numeric.decode(n.split("")),t=e.length;for(let n=0;n<t-2;n+=3)i+=cd(100*e[n]+10*e[n+1]+e[n+2],10);t%3==1?i+=cd(e[t-1],4):t%3==2&&(i+=cd(10*e[t-2]+e[t-1],7))}else if("alphanumeric"===r){const e=bd.alphabet.alphanumerc.decode(n.split("")),t=e.length;for(let n=0;n<t-1;n+=2)i+=cd(45*e[n]+e[n+1],11);t%2==1&&(i+=cd(e[t-1],6))}else{if("byte"!==r)throw new Error("encode: unsupported type");{const e=function(e){if("string"!=typeof e)throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array((new TextEncoder).encode(e))}(n);o=e.length,i=Array.from(e).map((e=>cd(e,8))).join("")}}const{capacity:s}=bd.capacity(e,t),a=cd(o,bd.lengthBits(e,r));let c=bd.modeBits[r]+a+i;if(c.length>s)throw new Error("Capacity overflow");c+="0".repeat(Math.min(4,Math.max(0,s-c.length))),c.length%8&&(c+="0".repeat(8-c.length%8));const d="1110110000010001";for(let e=0;c.length!==s;e++)c+=d[e%16];const l=Uint8Array.from(c.match(/(.{8})/g).map((e=>Number(`0b${e}`))));return _d(e,t).encode(l)}function xd(e,t,n,r,i=!1){const o=function(e,t,n,r=!1){const i=bd.size.encode(e);let o=new pd(i+2);const s=new pd(3).rect(0,3,!0).border(1,!1).border(1,!0).border(1,!1);o=o.embed(0,s).embed({x:-s.width,y:0},s).embed({x:0,y:-s.height},s),o=o.rectSlice(1,i);const a=new pd(1).rect(0,1,!0).border(1,!1).border(1,!0),c=bd.alignmentPatterns(e);for(const e of c)for(const t of c)void 0===o.data[e][t]&&o.embed({x:t-2,y:e-2},a);o=o.hLine({x:0,y:6},1/0,(({x:e},t)=>void 0===t?e%2==0:t)).vLine({x:6,y:0},1/0,(({y:e},t)=>void 0===t?e%2==0:t));{const e=bd.formatBits(t,n),s=t=>!r&&1==(e>>t&1);for(let e=0;e<6;e++)o.data[e][8]=s(e);for(let e=6;e<8;e++)o.data[e+1][8]=s(e);for(let e=8;e<15;e++)o.data[i-15+e][8]=s(e);for(let e=0;e<8;e++)o.data[8][i-e-1]=s(e);for(let e=8;e<9;e++)o.data[8][15-e-1+1]=s(e);for(let e=9;e<15;e++)o.data[8][15-e-1]=s(e);o.data[i-8][8]=!r}if(e>=7){const t=bd.versionBits(e);for(let e=0;e<18;e+=1){const n=!r&&1==(t>>e&1),s=Math.floor(e/3),a=e%3+i-8-3;o.data[s][a]=n,o.data[a][s]=n}}return o}(e,t,r,i);let s=0;const a=8*n.length;if(function(e,t,n){const r=e.height,i=wd[t];let o=-1,s=r-1;for(let t=r-1;t>0;t-=2){for(6==t&&(t=5);;s+=o){for(let r=0;r<2;r+=1){const o=t-r;void 0===e.data[s][o]&&n(o,s,i(o,s))}if(s+o<0||s+o>=r)break}o=-o}}(o,r,((e,t,r)=>{let i=!1;s<a&&(i=0!=(n[s>>>3]>>(7-s&7)&1),s++),o.data[t][e]=i!==r})),s!==a)throw new Error("QR: bytes left after draw");return o}function kd(e){const t=e.inverse(),n=e=>{let t=0;for(let n,r=0,i=1;r<e.length;r++)n===e[r]&&(i++,r!==e.length-1)||(i>=5&&(t+=i-5+3),n=e[r],i=1);return t};let r=0;e.data.forEach((e=>r+=n(e))),t.data.forEach((e=>r+=n(e)));let i=0,o=e.data;const s=e.width-1,a=e.height-1;for(let e=0;e<s;e++)for(let t=0;t<a;t++){const n=e+1,r=t+1;o[e][t]===o[n][t]&&o[n][t]===o[e][r]&&o[n][t]===o[n][r]&&(i+=3)}const c=e=>{const t=[!0,!1,!0,!0,!0,!1,!0],n=[!1,!1,!1,!1],r=[...t,...n],i=[...n,...t];let o=0;for(let t=0;t<e.length;t++)hd(e,r,t)&&(o+=40),hd(e,i,t)&&(o+=40);return o};let d=0;for(const t of e.data)d+=c(t);for(const e of t.data)d+=c(e);let l=0;e.rectRead(0,1/0,((e,t)=>l+=t?1:0));const u=l/(e.height*e.width)*100,h=10*Math.floor(Math.abs(u-50)/5);return r+i+d+h}function Md(e,t="raw",n={}){const r=void 0!==n.ecc?n.ecc:"medium";!function(e){if(!md.includes(e))throw new Error(`Invalid error correction mode=${e}. Expected: ${md}`)}(r);const i=void 0!==n.encoding?n.encoding:function(e){let t="numeric";for(let n of e)if(!bd.alphabet.numeric.has(n)&&(t="alphanumeric",!bd.alphabet.alphanumerc.has(n)))return"byte";return t}(e);!function(e){if(!gd.includes(e))throw new Error(`Encoding: invalid mode=${e}. Expected: ${gd}`);if("kanji"===e||"eci"===e)throw new Error(`Encoding: ${e} is not supported (yet?).`)}(i),void 0!==n.mask&&function(e){if(![0,1,2,3,4,5,6,7].includes(e)||!wd[e])throw new Error(`Invalid mask=${e}. Expected number [0..7]`)}(n.mask);let o,s=n.version,a=new Error("Unknown error");if(void 0!==s)!function(e){if(!Number.isSafeInteger(e)||e<1||e>40)throw new Error(`Invalid version=${e}. Expected number [1..40]`)}(s),o=Cd(s,r,e,i);else for(let t=1;t<=40;t++)try{o=Cd(t,r,e,i),s=t;break}catch(e){a=e}if(!s||!o)throw a;let c=function(e,t,n,r){if(void 0===r){const i=function(){let e,t=1/0;return{add(n,r){n>=t||(e=r,t=n)},get:()=>e,score:()=>t}}();for(let r=0;r<wd.length;r++)i.add(kd(xd(e,t,n,r,!0)),r);r=i.get()}if(void 0===r)throw new Error("Cannot find mask");return xd(e,t,n,r)}(s,r,o,n.mask);c.assertDrawn();const d=void 0===n.border?2:n.border;if(!Number.isSafeInteger(d))throw new Error("Wrong border type="+typeof d);if(c=c.border(d,!1),void 0!==n.scale&&(c=c.scale(n.scale)),"raw"===t)return c.data;if("ascii"===t)return c.toASCII();if("svg"===t)return c.toSVG();if("gif"===t)return c.toGIF();if("term"===t)return c.toTerm();throw new Error(`Unknown output: ${t}`)}const Ad=({link:e})=>{const t=Md(e,"ascii");return console.log(t),Ra(`[UI: InstallModal-nodejs()] qrcode url: ${e}`),{unmount:()=>{}}},Td=()=>(Ra("[UI: pendingModal-nodejs: PendingModal()] Please open the MetaMask wallet app and confirm the connection. Thank you!"),{unmount:()=>!1,updateOTPValue:e=>{""!==e&&Ra(`[UI: pendingModal-nodejs: PendingModal()] Choose the following value on your metamask mobile wallet: ${e}`)},mount:()=>!1});function Id(e,t){var n,r,i,o;e.connector||(Ra("[RemoteConnection: initializeConnector()] initialize connector"),e.connector=new Br({platformType:t.platformManager.getPlatformType(),communicationLayerPreference:t.communicationLayerPreference,transports:t.transports,dappMetadata:Object.assign(Object.assign({},t.dappMetadata),{source:t._source}),analytics:t.enableAnalytics,communicationServerUrl:t.communicationServerUrl,sdkVersion:hc.version,context:"dapp",ecies:t.ecies,storage:t.storage,logging:t.logging}),t.timer&&(Ra("[RemoteConnection: initializeConnector()] reset background timer",t.timer),null===(r=null===(n=t.timer)||void 0===n?void 0:n.stopBackgroundTimer)||void 0===r||r.call(n),null===(o=null===(i=t.timer)||void 0===i?void 0:i.runBackgroundTimer)||void 0===o||o.call(i,(()=>!1),1e4)))}function Pd(e){e.listeners.forEach((({event:t,handler:n})=>{var r;null===(r=e.connector)||void 0===r||r.off(t,n)})),e.listeners=[]}function Rd(e,t,n){return l(this,void 0,void 0,(function*(){return new Promise(((r,i)=>{if(!e.connector)return void i(new Error("No connector available"));Ra("[RemoteConnection: connectWithModalInstaller()]",{state:e,options:t,linkParams:n});const o=`${e.useDeeplink?mc:pc}?${n}`;!function(e,t,n){var r,i,o,s;e.installModal=null===(i=(r=t.modals).install)||void 0===i?void 0:i.call(r,{link:n,preferDesktop:e.preferDesktop,installer:t.getMetaMaskInstaller(),terminate:()=>{Ra("[RemoteConnection: showInstallModal() => terminate()] terminate connection"),t.sdk.terminate().catch((e=>{console.warn("[MMSDK] failed to terminate connection",e)}))},debug:e.developerMode,connectWithExtension:()=>{var e;return null===(e=t.connectWithExtensionProvider)||void 0===e||e.call(t),!1},onAnalyticsEvent:({event:n,params:r})=>{var i,o,s;const a=Object.assign(Object.assign({},r),{sdkVersion:t.sdk.getVersion(),dappId:null===(i=t.dappMetadata)||void 0===i?void 0:i.name,source:t._source,url:null===(o=t.dappMetadata)||void 0===o?void 0:o.url});null===(s=e.analytics)||void 0===s||s.send({event:n,params:a})}}),null===(s=null===(o=e.installModal)||void 0===o?void 0:o.mount)||void 0===s||s.call(o,n)}(e,t,o),t.sdk.once(exports.EventType.PROVIDER_UPDATE,(e=>l(this,void 0,void 0,(function*(){if(Ra("[RemoteConnection: connectWithModalInstaller()] once provider_update -- resolving startConnection promise"),e!==exports.PROVIDER_UPDATE_TYPE.TERMINATE)i(e);else{i({code:4001,message:"User rejected the request."})}})))),e.connector.once(exports.EventType.AUTHORIZED,(()=>{r()})),e.connector.once(exports.EventType.REJECTED,(()=>{i(exports.EventType.REJECTED)})),e.connector.once(exports.EventType.CLIENTS_READY,(()=>l(this,void 0,void 0,(function*(){Ra("[RemoteConnection: connectWithModalInstaller()] once clients_ready -- resolving startConnection promise"),r()}))))}))}))}function Od(e,t){function n(t,n){var r;null===(r=e.connector)||void 0===r||r.on(t,n),e.listeners.push({event:t,handler:n})}e.connector&&(Pd(e),n(exports.EventType.WALLET_INIT,(({accounts:e,chainId:t})=>l(this,void 0,void 0,(function*(){Ra(`[RemoteConnection: setupListeners() => EventType.WALLET_INIT] 'wallet_init' accounts=${e} chainId=${t}`);const n=ac.getProvider();n._setConnected();const r={accounts:e,chainId:t,isUnlocked:!1};n._initializeState(r),n.emit("chainChanged",t),n.emit("accountsChanged",e)})))),n(exports.EventType.AUTHORIZED,(()=>l(this,void 0,void 0,(function*(){var t,n,r,i;try{Ra("[RemoteConnection: setupListeners() => EventType.AUTHORIZED] 'authorized' closing modals",e.pendingModal,e.installModal);const o=ac.getProvider();o._setConnected(),null===(n=null===(t=e.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),null===(i=null===(r=e.installModal)||void 0===r?void 0:r.unmount)||void 0===i||i.call(r,!1),e.otpAnswer=void 0,e.authorized=!0,Ra("[RemoteConnection: setupListeners() => EventType.AUTHORIZED] 'authorized' provider.state",o.getState()),yield o.forceInitializeState()}catch(e){}})))),n(exports.EventType.TERMINATE,(()=>{var t,n,r,i,o;null===(n=null===(t=e.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),null===(i=null===(r=e.installModal)||void 0===r?void 0:r.unmount)||void 0===i||i.call(r,!0),e.pendingModal=void 0,e.installModal=void 0,e.otpAnswer=void 0,null===(o=e.connector)||void 0===o||o.disconnect({terminate:!0}),e.authorized=!1;ac.getProvider().handleDisconnect({terminate:!0}),Pd(e),Ra("[RemoteConnection: setupListeners()] All listeners cleaned up")})))}function Nd(e,t,{initialCheck:n,connectWith:r}={}){var i,o,s,a,c,d,u,h,f,p,m,g,v,y,E,b,w,S,_;return l(this,void 0,void 0,(function*(){try{if(Id(e,t),!e.connector)throw new Error("no connector defined");Od(e);const C=ac.getProvider();e.authorized=!1,C.emit("connecting");const x=yield null===(i=e.connector)||void 0===i?void 0:i.originatorSessionConnect();Ra(`[RemoteConnection: startConnection()] after originatorSessionConnect initialCheck=${n}`,x);let k=null!==(o=null==x?void 0:x.channelId)&&void 0!==o?o:"",M=null!==(a=null===(s=e.connector.getKeyInfo())||void 0===s?void 0:s.ecies.public)&&void 0!==a?a:"",A=null!==(d=null===(c=e.connector.getKeyInfo())||void 0===c?void 0:c.ecies.private)&&void 0!==d?d:"";if(n&&!x)return Promise.resolve();if(!x&&!n){const t=yield e.connector.generateChannelIdConnect();k=null!==(u=t.channelId)&&void 0!==u?u:"",M=null!==(h=t.pubKey)&&void 0!==h?h:"",A=null!==(f=t.privKey)&&void 0!==f?f:"";const n=Date.now();null===(p=e.connector.state.storageManager)||void 0===p||p.persistChannelConfig({channelId:k,localKey:A,lastActive:n,validUntil:n+ir})}if(n&&(null==x?void 0:x.channelId))return(null===(m=e.connector)||void 0===m?void 0:m.isConnected())||(Ra(`[RemoteConnection: startConnection()] reconnecting to channel initialCheck=${n}`,x),yield null===(g=e.connector)||void 0===g?void 0:g.connectToChannel({channelId:k})),Promise.resolve();x&&!(null===(v=e.connector)||void 0===v?void 0:v.isConnected())&&(Ra("[RemoteConnection: startConnection()] reconnecting to channel",x),yield null===(y=e.connector)||void 0===y?void 0:y.connectToChannel({channelId:k}));const T=(null===(E=e.platformManager)||void 0===E?void 0:E.isSecure())?"":"&t=q",I=hc.version,{iconUrl:P,name:R,url:O,scheme:N}=t.dappMetadata||{},D=null===(b=e.platformManager)||void 0===b?void 0:b.getPlatformType();let L="N/A";"undefined"!=typeof window&&window.location&&window.location.hostname?L=window.location.hostname:void 0!==R?L=R:void 0!==O&&(L=O);const B={url:null!=O?O:"",title:null!=R?R:"",icon:P,scheme:null!=N?N:"",apiVersion:I,dappId:L||O||"N/A",platform:null!=D?D:"",source:null!==(w=t._source)&&void 0!==w?w:""},K=Rc(JSON.stringify(B));let j=`channelId=${k}&v=2&comm=${null!==(S=e.communicationLayerPreference)&&void 0!==S?S:""}&pubkey=${M}${T}&originatorInfo=${K}`;if(r){j+=`&rpc=${Rc(JSON.stringify(r))}`;const t=e.connector.getRPCMethodTracker();t&&(t[`${r.id}`]=Object.assign(Object.assign({},r),{id:`${r.id}`,timestamp:Date.now()}))}const $=encodeURI(j),H=`${e.useDeeplink?mc:pc}?${j}`;return e.qrcodeLink=H,e.developerMode&&Ra(`[RemoteConnection: startConnection()] qrcodeLink=${$}`),C.emit("display_uri",H),(null===(_=e.platformManager)||void 0===_?void 0:_.isSecure())?(yield function(e,t){var n,r;return l(this,void 0,void 0,(function*(){const i=`${pc}?${t}`,o=`${mc}?${t}`;null===(r=null===(n=e.platformManager)||void 0===n?void 0:n.openDeeplink)||void 0===r||r.call(n,i,o,"_self")}))}(e,$),new Promise(((t,n)=>{var r,i,o;(null===(r=e.connector)||void 0===r?void 0:r.isAuthorized())?t():(null===(i=e.connector)||void 0===i||i.once(exports.EventType.AUTHORIZED,(()=>{t()})),null===(o=e.connector)||void 0===o||o.once(exports.EventType.REJECTED,(()=>{n(exports.EventType.REJECTED)})))}))):Rd(e,t,$)}catch(e){throw console.error("[startConnection] error",e),e}}))}class Dd{constructor(e){var t,n,r;this.state={connector:void 0,qrcodeLink:void 0,analytics:void 0,developerMode:!1,authorized:!1,reconnection:!1,preferDesktop:!1,deeplinkProtocol:!1,listeners:[],communicationLayerPreference:void 0,platformManager:void 0,pendingModal:void 0,installModal:void 0,otpAnswer:void 0},this.options=e;const i=!0===(null===(t=e.logging)||void 0===t?void 0:t.developerMode)||!0===(null===(n=e.logging)||void 0===n?void 0:n.sdk);this.state.developerMode=i,this.state.analytics=e.analytics,this.state.preferDesktop=null!==(r=e.preferDesktop)&&void 0!==r&&r,this.state.useDeeplink=e.sdk.options.useDeeplink,this.state.communicationLayerPreference=e.communicationLayerPreference,this.state.platformManager=e.platformManager,e.modals.install||(e.modals.install=Ad),e.modals.otp||(e.modals.otp=Td)}startConnection(e){return l(this,void 0,void 0,(function*(){return Nd(this.state,this.options,e)}))}initRemoteCommunication({sdkInstance:e}){var t,n,r;return l(this,void 0,void 0,(function*(){const i=yield null===(n=null===(t=e.options.storage)||void 0===t?void 0:t.storageManager)||void 0===n?void 0:n.getPersistedChannelConfig();if(!this.options.ecies){const e={privateKey:null==i?void 0:i.localKey};this.options.ecies=e}Id(this.state,this.options),yield null===(r=this.getConnector())||void 0===r?void 0:r.initFromDappStorage(),Od(this.state,this.options)}))}showActiveModal(){return function(e){var t,n,r,i;e.authorized?Ra("[RemoteConnection: showActiveModal()] already authorized"):e.pendingModal?null===(n=(t=e.pendingModal).mount)||void 0===n||n.call(t):e.installModal&&(null===(i=(r=e.installModal).mount)||void 0===i||i.call(r,e.qrcodeLink||""))}(this.state)}closeModal(){var e,t,n,r;null===(t=null===(e=this.state.pendingModal)||void 0===e?void 0:e.unmount)||void 0===t||t.call(e),null===(r=null===(n=this.state.installModal)||void 0===n?void 0:n.unmount)||void 0===r||r.call(n,!1)}getUniversalLink(){if(!this.state.qrcodeLink)throw new Error("connection not started. run startConnection() first.");return this.state.qrcodeLink}getChannelConfig(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.getChannelConfig()}getKeyInfo(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.getKeyInfo()}getConnector(){if(!this.state.connector)throw new Error("invalid remote connector");return this.state.connector}getPlatformManager(){if(!this.state.platformManager)throw new Error("PlatformManager not available");return this.state.platformManager}isConnected(){var e;return(null===(e=this.state.connector)||void 0===e?void 0:e.isReady())||!1}isAuthorized(){var e;return(null===(e=this.state.connector)||void 0===e?void 0:e.isAuthorized())||!1}isPaused(){var e;return null===(e=this.state.connector)||void 0===e?void 0:e.isPaused()}disconnect(e){var t,n,r;Ra("[RemoteConnection: disconnect()]",e),(null==e?void 0:e.terminate)&&(ac.getProvider().handleDisconnect({terminate:!0}),null===(n=null===(t=this.state.pendingModal)||void 0===t?void 0:t.unmount)||void 0===n||n.call(t),this.state.otpAnswer=void 0),null===(r=this.state.connector)||void 0===r||r.disconnect(e),function(e){Ra("[RemoteConnection: cleanupConnector()] cleaning up connector"),e.connector&&(Pd(e),e.connector.disconnect({terminate:!0}).catch((e=>{Ra("[RemoteConnection: cleanupConnector()] error disconnecting connector",e)})))}(this.state)}}function Ld(e){var n,r,i,o,s,a,c,d,u,h,f;return l(this,void 0,void 0,(function*(){const{options:p}=e;if(p.logging=null!==(n=p.logging)&&void 0!==n?n:{},p.communicationLayerPreference=null!==(r=p.communicationLayerPreference)&&void 0!==r?r:exports.CommunicationLayerPreference.SOCKET,void 0!==p.enableDebug&&(t.enable("MM_SDK"),console.warn("enableDebug is removed. Please use enableAnalytics instead.")),p.enableAnalytics=null===(i=p.enableAnalytics)||void 0===i||i,p.injectProvider=null===(o=p.injectProvider)||void 0===o||o,p.shouldShimWeb3=null===(s=p.shouldShimWeb3)||void 0===s||s,p.extensionOnly=null===(a=p.extensionOnly)||void 0===a||a,p.useDeeplink=null===(c=p.useDeeplink)||void 0===c||c,p.storage=null!==(d=p.storage)&&void 0!==d?d:{enabled:!0},p.headless){t("[MetaMaskSDK: performSDKInitialization()] headless mode enabled");const e=()=>{},n={install:()=>({mount:e,unmount:e})},r={installer:e};p.modals=n,p.ui=r}const m=!0===(null===(u=p.logging)||void 0===u?void 0:u.developerMode);e.debug=(null===(h=p.logging)||void 0===h?void 0:h.sdk)||m,Ra("[MetaMaskSDK: performSDKInitialization()] options",e.options);const g=Object.assign({},p.logging);m&&(g.sdk=!0,g.eciesLayer=!0,g.keyExchangeLayer=!0,g.remoteLayer=!0,g.serviceLayer=!0,g.plaintext=!0),yield function(e){var t;return l(this,void 0,void 0,(function*(){const{options:n}=e;e.platformManager=new cc({useDeepLink:null!==(t=n.useDeeplink)&&void 0!==t&&t,preferredOpenLink:n.openDeeplink,debug:e.debug})}))}(e),yield function(e){var t,n,r,i,o,s,a,c,d;return l(this,void 0,void 0,(function*(){const{options:l}=e,u=null===(t=e.platformManager)||void 0===t?void 0:t.getPlatformType();e.analytics=new $c({serverUrl:null!==(n=l.communicationServerUrl)&&void 0!==n?n:nr,enabled:l.enableAnalytics,originatorInfo:{url:null!==(r=l.dappMetadata.url)&&void 0!==r?r:"",title:null!==(i=l.dappMetadata.name)&&void 0!==i?i:"",dappId:"undefined"==typeof window||void 0===window.location?null!==(c=null!==(s=null===(o=l.dappMetadata)||void 0===o?void 0:o.name)&&void 0!==s?s:null===(a=l.dappMetadata)||void 0===a?void 0:a.url)&&void 0!==c?c:"N/A":window.location.hostname,platform:null!=u?u:"",source:null!==(d=l._source)&&void 0!==d?d:""}})}))}(e),yield function(e){var t;return l(this,void 0,void 0,(function*(){const{options:n}=e;!0!==(null===(t=n.storage)||void 0===t?void 0:t.enabled)||n.storage.storageManager||(n.storage.storageManager=yield dc(n.storage))}))}(e),yield function(e){return l(this,void 0,void 0,(function*(){const{options:t}=e,n=/^(http|https):\/\/[^\s]*$/;if(t.dappMetadata){t.dappMetadata.iconUrl&&!n.test(t.dappMetadata.iconUrl)&&(console.warn("Invalid dappMetadata.iconUrl: URL must start with http:// or https://"),t.dappMetadata.iconUrl=void 0),t.dappMetadata.base64Icon&&t.dappMetadata.base64Icon.length>Uc&&(console.warn("Invalid dappMetadata.base64Icon: Base64-encoded icon string length must be less than 163400 characters"),t.dappMetadata.base64Icon=void 0),t.dappMetadata.url&&!n.test(t.dappMetadata.url)&&console.warn("Invalid dappMetadata.url: URL must start with http:// or https://");const e=Hc();if(e&&!t.dappMetadata.iconUrl&&!t.dappMetadata.base64Icon){const n=`${window.location.protocol}//${window.location.host}${e}`;t.dappMetadata.iconUrl=n}}e.dappMetadata=t.dappMetadata}))}(e),yield Vc(e),yield Gc(e);const{metamaskBrowserExtension:v,preferExtension:y,shouldReturn:E}=yield function(e){var t,n,r,i;return l(this,void 0,void 0,(function*(){const{options:o}=e;let s,a=!1,c=!1;if("undefined"!=typeof window&&window.ethereum&&!(null===(t=e.platformManager)||void 0===t?void 0:t.isMetaMaskMobileWebView())){a="extension"===localStorage.getItem(_c);try{s=yield Wc({mustBeMetaMask:!0,sdkInstance:e}),window.extension=s,s.on(kc.CHAIN_CHANGED,(t=>{Ra(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE chainChanged chainId=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(kc.CHAIN_CHANGED,t)})),s.on(kc.ACCOUNTS_CHANGED,(t=>l(this,void 0,void 0,(function*(){var n;Ra(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE accountsChanged accounts=${t}`);const r=Boolean(e.sdkProvider),i=Boolean(e.extensionActive);if(r&&e.getMobileProvider().emit(kc.ACCOUNTS_CHANGED,t),i&&0===(null==t?void 0:t.length)&&0===(yield null===(n=e.getProvider())||void 0===n?void 0:n.request({method:yc.WALLET_GETPERMISSIONS,params:[]})).length)try{yield e.terminate()}catch(e){Ra("[MetaMaskSDK: setupExtensionPreferences()] error terminating on permissions revoked",e)}})))),s.on(kc.DISCONNECT,(t=>{Ra(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE disconnect error=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(kc.DISCONNECT,t)})),s.on(kc.CONNECT,(t=>{Ra(`[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE connect args=${t}`),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(kc.CONNECT,t)})),s.on(kc.CONNECTED,(t=>{Ra("[MetaMaskSDK: setupExtensionPreferences()] PROPAGATE connected",t),Boolean(e.sdkProvider)&&e.getMobileProvider().emit(kc.CONNECTED,t)}))}catch(e){window.extension=void 0}}else(null===(n=e.platformManager)||void 0===n?void 0:n.isMetaMaskMobileWebView())&&(null===(r=e.analytics)||void 0===r||r.send({event:lr.SDK_USE_INAPP_BROWSER}),e.activeProvider=qc({provider:window.ethereum,sdkInstance:e}),e._initialized=!0,c=!0);return s&&o.extensionOnly&&(Ra("[MetaMaskSDK: setupExtensionPreferences()] EXTENSION ONLY --- prevent sdk initialization"),null===(i=e.analytics)||void 0===i||i.send({event:lr.SDK_USE_EXTENSION}),e.activeProvider=s,e.extensionActive=!0,e.extension=s,e._initialized=!0,c=!0),{preferExtension:a,shouldReturn:c,metamaskBrowserExtension:s}}))}(e);if(E)Ra("[MetaMaskSDK: performSDKInitialization()] shouldReturn=true --- prevent sdk initialization");else{yield function(e,t){var n,r,i,o,s;return l(this,void 0,void 0,(function*(){const{options:a}=e,c=Object.assign({},a.logging);e.remoteConnection=new Dd({preferDesktop:null!==(n=a.preferDesktop)&&void 0!==n&&n,communicationLayerPreference:null!==(r=a.communicationLayerPreference)&&void 0!==r?r:exports.CommunicationLayerPreference.SOCKET,analytics:e.analytics,dappMetadata:a.dappMetadata,_source:a._source,enableAnalytics:null===(i=a.enableAnalytics)||void 0===i||i,timer:a.timer,sdk:e,platformManager:e.platformManager,transports:a.transports,communicationServerUrl:a.communicationServerUrl,storage:null!==(o=a.storage)&&void 0!==o?o:{enabled:!0},getMetaMaskInstaller:()=>{if(!e.installer)throw new Error("Invalid SDK status -- installer not initialized");return e.installer},logging:c,connectWithExtensionProvider:void 0===t?void 0:()=>Pc(e),modals:Object.assign(Object.assign({},a.modals),{onPendingModalDisconnect:e.terminate.bind(e)})}),yield e.remoteConnection.initRemoteCommunication({sdkInstance:e}),e.installer=new ad({remote:e.remoteConnection,preferDesktop:null!==(s=a.preferDesktop)&&void 0!==s&&s,platformManager:e.platformManager,debug:e.debug})}))}(e,v),yield Kc(e),yield function(e,t){var n,r;return l(this,void 0,void 0,(function*(){const{options:i}=e;t?(Ra("[MetaMaskSDK: handleAutoAndExtensionConnections()] preferExtension is detected -- connect with it."),null===(n=e.analytics)||void 0===n||n.send({event:lr.SDK_EXTENSION_UTILIZED}),Pc(e).catch((e=>{console.warn("Can't connect with MetaMask extension...",e),localStorage.removeItem(_c)}))):i.checkInstallationImmediately&&((null===(r=e.platformManager)||void 0===r?void 0:r.isDesktopWeb())?(Ra("[MetaMaskSDK: handleAutoAndExtensionConnections()] checkInstallationImmediately"),e.connect().catch((e=>{Ra(`[MetaMaskSDK: handleAutoAndExtensionConnections()] checkInstallationImmediately --- IGNORED --- error on autoconnect _err=${e}`)}))):console.warn("[handleAutoAndExtensionConnections()] checkInstallationImmediately --- IGNORED --- only for web desktop")),e._initialized=!0}))}(e,y);try{yield null===(f=e.remoteConnection)||void 0===f?void 0:f.startConnection({initialCheck:!0})}catch(e){console.error("[MetaMaskSDK: setupRemoteConnectionAndInstaller()] Error while checking installation",e)}e.emit(sc.ProviderUpdate,exports.PROVIDER_UPDATE_TYPE.INITIALIZED)}}))}class Bd extends i{constructor(e={storage:{enabled:!0},injectProvider:!0,forceInjectProvider:!1,enableAnalytics:!0,shouldShimWeb3:!0,useDeeplink:!0,extensionOnly:!0,headless:!1,dappMetadata:{name:"",url:"",iconUrl:""},_source:fc,i18nOptions:{enabled:!1}}){var n,r,i;super(),this.extensionActive=!1,this._initialized=!1,this.sdkInitPromise=void 0,this.debug=!1,this.readonlyRPCCalls=!1,this.availableLanguages=["en"],t.disable();const o=!0===(null===(n=e.logging)||void 0===n?void 0:n.developerMode);if(((null===(r=e.logging)||void 0===r?void 0:r.sdk)||o)&&t.enable("MM_SDK"),Ra("[MetaMaskSDK: constructor()]: begin."),this.setMaxListeners(50),!(null===(i=e.dappMetadata)||void 0===i?void 0:i.url)){if("undefined"==typeof window||"undefined"==typeof document)throw new Error("You must provide dAppMetadata url");e.dappMetadata=Object.assign(Object.assign({},e.dappMetadata),{url:`${window.location.protocol}//${window.location.host}`})}this.options=e,this.options._source||(e._source=fc),this.init().then((()=>{Ra("[MetaMaskSDK: constructor()]: initialized successfully."),"undefined"!=typeof window&&(window.mmsdk=this)})).catch((e=>{console.error("[MetaMaskSDK: constructor()] error during initialization",e)}))}init(){return l(this,void 0,void 0,(function*(){return function(e){var t;return l(this,void 0,void 0,(function*(){if("undefined"!=typeof window&&(null===(t=window.mmsdk)||void 0===t?void 0:t.isInitialized()))return Ra("[MetaMaskSDK: initializeMetaMaskSDK()] already initialized"),Promise.resolve(window.mmsdk);if(e._initialized)return Ra("[MetaMaskSDK: initializeMetaMaskSDK()] already initialized"),e.sdkInitPromise;if(e.sdkInitPromise)return Ra("[MetaMaskSDK: initializeMetaMaskSDK()] already initializing"),e.sdkInitPromise;try{e.sdkInitPromise=Ld(e),yield e.sdkInitPromise}catch(e){throw console.error(e),e}return e.sdkInitPromise}))}(this)}))}isExtensionActive(){return this.extensionActive}checkExtensionAvailability(){var e;return"undefined"!=typeof window&&Boolean(null===(e=window.ethereum)||void 0===e?void 0:e.isMetaMask)}connect(){return l(this,void 0,void 0,(function*(){return function(e){return l(this,void 0,void 0,(function*(){if(e._initialized||(Ra("[MetaMaskSDK: connect()] provider not ready -- wait for init()"),yield e.init()),Ra(`[MetaMaskSDK: connect()] isExtensionActive=${e.isExtensionActive()} activeProvider`,e.activeProvider),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");const t=e.activeProvider.getSelectedAddress();return t?[t]:e.activeProvider.request({method:yc.ETH_REQUESTACCOUNTS,params:[]})}))}(this)}))}connectAndSign({msg:e}){return l(this,void 0,void 0,(function*(){return Ic({instance:this,msg:e})}))}connectWith(e){return l(this,void 0,void 0,(function*(){return function({instance:e,rpc:t}){return l(this,void 0,void 0,(function*(){if(e._initialized||(Ra("[MetaMaskSDK: connectWith()] provider not ready -- wait for init()"),yield e.init()),Ra(`[MetaMaskSDK: connectWith()] method: ${t.method} rpc=${t}`),!e.activeProvider)throw new Error("SDK state invalid -- undefined provider");return e.activeProvider.request({method:yc.METAMASK_CONNECTWITH,params:[t]})}))}({instance:this,rpc:e})}))}resume(){return function(e){var t,n,r;return l(this,void 0,void 0,(function*(){if(!(null===(n=null===(t=e.remoteConnection)||void 0===t?void 0:t.getConnector())||void 0===n?void 0:n.isReady()))return Ra("[MetaMaskSDK: resume()] channel is not ready -- starting connection"),void(null===(r=e.remoteConnection)||void 0===r||r.startConnection());Ra("[MetaMaskSDK: resume()] channel is ready")}))}(this)}disconnect(){return console.warn("MetaMaskSDK.disconnect() is deprecated, use terminate()"),this.terminate()}isAuthorized(){var e;null===(e=this.remoteConnection)||void 0===e||e.isAuthorized()}terminate(){return function(e){var t,n,r;return l(this,void 0,void 0,(function*(){if(!(null===(t=e.platformManager)||void 0===t?void 0:t.isMetaMaskMobileWebView())){if(Tc&&(window.localStorage.removeItem(_c),window.localStorage.removeItem(xc),window.localStorage.removeItem(Cc)),e.extensionActive){try{yield null===(n=e.activeProvider)||void 0===n?void 0:n.request({method:yc.WALLET_REVOKEPERMISSIONS,params:[{eth_accounts:{}}]})}catch(e){Ra("[MetaMaskSDK: terminate()] error revoking permissions",e)}return e.options.extensionOnly?(e.emit(sc.ProviderUpdate,exports.PROVIDER_UPDATE_TYPE.TERMINATE),void Ra("[MetaMaskSDK: terminate()] extensionOnly --- prevent switching providers")):(e.activeProvider=e.sdkProvider,window.ethereum=e.activeProvider,e.extensionActive=!1,void e.emit(sc.ProviderUpdate,exports.PROVIDER_UPDATE_TYPE.TERMINATE))}e.emit(sc.ProviderUpdate,exports.PROVIDER_UPDATE_TYPE.TERMINATE),Ra(`[MetaMaskSDK: terminate()] remoteConnection=${e.remoteConnection}`),null===(r=e.remoteConnection)||void 0===r||r.disconnect({terminate:!0,sendMessage:!0})}}))}(this)}isInitialized(){return this._initialized}setReadOnlyRPCCalls(e){this.readonlyRPCCalls=e}hasReadOnlyRPCCalls(){return this.readonlyRPCCalls}getProvider(){if(this.activeProvider)return this.activeProvider;console.warn("MetaMaskSDK: No active provider found")}getMobileProvider(){if(!this.sdkProvider)throw new Error("SDK state invalid -- undefined mobile provider");return this.sdkProvider}getUniversalLink(){var e;const t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getUniversalLink();if(!t)throw new Error("No Universal Link available, please call eth_requestAccounts first.");return t}getChannelId(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getChannelConfig())||void 0===t?void 0:t.channelId}getRPCHistory(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getRPCMethodTracker()}getVersion(){return hc.version}getWalletStatus(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getConnectionStatus()}_getChannelConfig(){var e;return null===(e=this.remoteConnection)||void 0===e?void 0:e.getChannelConfig()}_ping(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.ping()}_keyCheck(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.keyCheck()}_getServiceStatus(){var e,t;return null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t?void 0:t.getServiceStatus()}_getRemoteConnection(){return this.remoteConnection}_getDappMetadata(){return this.dappMetadata}_getKeyInfo(){var e;return null===(e=this.remoteConnection)||void 0===e?void 0:e.getKeyInfo()}_resetKeys(){var e,t;null===(t=null===(e=this.remoteConnection)||void 0===e?void 0:e.getConnector())||void 0===t||t.resetKeys()}_getConnection(){return this.remoteConnection}emit(e,t){return super.emit(e,t)}on(e,t){return super.on(e,t)}}var Kd=Object.freeze({__proto__:null,StorageManagerWeb:class{constructor({enabled:e}={enabled:!1}){this.enabled=!1,this.enabled=e}persistChannelConfig(e){return l(this,void 0,void 0,(function*(){const t=JSON.stringify(e);Ra(`[StorageManagerWeb: persistChannelConfig()] enabled=${this.enabled}`,e),localStorage.setItem(Sc,t)}))}getPersistedChannelConfig(){return l(this,void 0,void 0,(function*(){let e;try{if(Ra(`[StorageManagerWeb: getPersistedChannelConfig()] enabled=${this.enabled}`),e=localStorage.getItem(Sc),Ra("[StorageManagerWeb: getPersistedChannelConfig()]",e),!e)return;const t=JSON.parse(e);return Ra("[StorageManagerWeb: getPersistedChannelConfig()] channelConfig",t),t}catch(e){return void console.error("[StorageManagerWeb: getPersistedChannelConfig()] Can't find existing channel config",e)}}))}persistAccounts(e){return l(this,void 0,void 0,(function*(){Ra(`[StorageManagerWeb: persistAccounts()] enabled=${this.enabled}`,e);const t=JSON.stringify(e);localStorage.setItem(Cc,t)}))}getCachedAccounts(){return l(this,void 0,void 0,(function*(){try{const e=localStorage.getItem(Cc);return e?JSON.parse(e):[]}catch(e){throw console.error("[StorageManagerWeb: getCachedAccounts()] Error reading cached accounts",e),e}}))}persistChainId(e){return l(this,void 0,void 0,(function*(){Ra(`[StorageManagerWeb: persistChainId()] enabled=${this.enabled}`,e),localStorage.setItem(xc,e)}))}getCachedChainId(){return l(this,void 0,void 0,(function*(){try{const e=localStorage.getItem(xc);return null!=e?e:void 0}catch(e){throw console.error("[StorageManagerWeb: getCachedChainId()] Error reading cached chainId",e),e}}))}terminate(){return l(this,void 0,void 0,(function*(){Ra(`[StorageManagerWeb: terminate()] enabled=${this.enabled}`),localStorage.removeItem(Sc)}))}}});exports.DEFAULT_SERVER_URL=nr,exports.MetaMaskSDK=Bd,exports.MetaMaskSDKEvent=sc,exports.SDKProvider=lc,exports.default=Bd;
//# sourceMappingURL=metamask-sdk.js.map
