export declare enum PROVIDER_UPDATE_TYPE {
    /**
     * Emitted when the sdk is terminated.
     */
    TERMINATE = "terminate",
    /**
     * Emitted when the sdk is initialized via extension.
     */
    EXTENSION = "extension",
    /**
     * Emitted when the sdk is initialized via any providers (extension / mobile / in-app browser ).
     */
    INITIALIZED = "initialized"
}
//# sourceMappingURL=ProviderUpdateType.d.ts.map