import { MetaMaskSDK } from '../../../sdk';
/**
 * Sets up the analytics instance for the MetaMask SDK.
 *
 * This function initializes an Analytics object and attaches it to the MetaMask SDK instance.
 * The analytics object is configured based on various options like the server URL, debug settings, and Dapp metadata.
 *
 * @param instance The MetaMaskSDK instance for which analytics will be set up.
 * @returns void
 * @async
 */
export declare function setupAnalytics(instance: MetaMaskSDK): Promise<void>;
//# sourceMappingURL=setupAnalytics.d.ts.map