{"version": 3, "file": "checkCrossOriginOpenerPolicy.js", "sourceRoot": "", "sources": ["../../src/util/checkCrossOriginOpenerPolicy.ts"], "names": [], "mappings": "AAAA,MAAM,kBAAkB,GAAG;;+GAEoF,CAAC;AAEhH;;;;;;;;;;;;;;GAcG;AACH,MAAM,iBAAiB,GAAG,GAAG,EAAE;IAC7B,IAAI,uBAA2C,CAAC;IAEhD,OAAO;QACL,0BAA0B,EAAE,GAAG,EAAE;YAC/B,IAAI,uBAAuB,KAAK,SAAS,EAAE,CAAC;gBAC1C,OAAO,WAAW,CAAC;YACrB,CAAC;YAED,OAAO,uBAAuB,CAAC;QACjC,CAAC;QACD,4BAA4B,EAAE,KAAK,IAAI,EAAE;YACvC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,0BAA0B;gBAC1B,uBAAuB,GAAG,iBAAiB,CAAC;gBAC5C,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACnE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;oBAChC,MAAM,EAAE,MAAM;iBACf,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC5D,CAAC;gBAED,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBAClE,uBAAuB,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,MAAM,CAAC;gBAE3C,IAAI,uBAAuB,KAAK,aAAa,EAAE,CAAC;oBAC9C,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;gBACtF,uBAAuB,GAAG,OAAO,CAAC;YACpC,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,EAAE,4BAA4B,EAAE,0BAA0B,EAAE,GAAG,iBAAiB,EAAE,CAAC"}