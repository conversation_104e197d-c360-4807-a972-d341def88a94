'use client'

import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { dbHelpers } from '@/lib/supabase'

interface SellOrder {
  id: string
  amount: number
  rate: number
  user_wallet: string
  created_at: string
  users: {
    wallet: string
    upi_id?: string
    bank_details?: string
  }
}

interface BuyUSDTFlowProps {
  onClose: () => void
  onSuccess: () => void
}

export default function BuyUSDTFlow({ onClose, onSuccess }: BuyUSDTFlowProps) {
  const { address } = useAccount()
  const [sellOrders, setSellOrders] = useState<SellOrder[]>([])
  const [selectedOrder, setSelectedOrder] = useState<SellOrder | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [step, setStep] = useState<'orders' | 'payment' | 'proof' | 'success'>('orders')
  const [buyAmount, setBuyAmount] = useState('')
  const [proofFile, setProofFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    loadSellOrders()
  }, [])

  const loadSellOrders = async () => {
    try {
      setLoading(true)
      const { data, error } = await dbHelpers.getSellOrders()
      
      if (error) {
        setError('Failed to load sell orders: ' + error.message)
        return
      }

      setSellOrders(data || [])
    } catch (err) {
      setError('Failed to load sell orders')
      console.error('Load sell orders error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSelectOrder = (order: SellOrder) => {
    setSelectedOrder(order)
    setBuyAmount(order.amount.toString())
    setStep('payment')
  }

  const handlePaymentMade = () => {
    setStep('proof')
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select an image file')
        return
      }
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB')
        return
      }
      setProofFile(file)
      setError('')
    }
  }

  const handleSubmitProof = async () => {
    if (!proofFile || !selectedOrder || !address) {
      setError('Missing required information')
      return
    }

    setUploading(true)
    setError('')

    try {
      // Upload proof to Supabase
      const { data, error } = await dbHelpers.uploadProof(
        proofFile,
        selectedOrder.id,
        address
      )

      if (error) {
        setError('Failed to upload proof: ' + error.message)
        return
      }

      console.log('Proof uploaded successfully:', data)
      setStep('success')
      
      setTimeout(() => {
        onSuccess()
        onClose()
      }, 3000)

    } catch (err) {
      console.error('Upload proof error:', err)
      setError('Failed to upload proof')
    } finally {
      setUploading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatWallet = (wallet: string) => {
    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`
  }

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <div className="animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p>Loading sell orders...</p>
      </div>
    )
  }

  if (step === 'payment' && selectedOrder) {
    const totalAmount = parseFloat(buyAmount) * selectedOrder.rate

    return (
      <div className="max-w-md mx-auto">
        <div className="mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
          <h2 className="text-2xl font-bold mb-2 text-center text-green-400">Payment Details</h2>
          <p className="text-gray-300 text-center text-sm">
            Make payment to the seller
          </p>
        </div>

        {/* Order Summary */}
        <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-blue-400">Order Summary</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>USDT Amount:</span>
              <span>{buyAmount} USDT</span>
            </div>
            <div className="flex justify-between">
              <span>Rate:</span>
              <span>₹{selectedOrder.rate} per USDT</span>
            </div>
            <div className="flex justify-between font-semibold text-blue-400">
              <span>Total to Pay:</span>
              <span>₹{totalAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Seller:</span>
              <span>{formatWallet(selectedOrder.user_wallet)}</span>
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div className="mb-6 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-yellow-400">Payment Information</h3>
          
          {selectedOrder.users.upi_id && (
            <div className="mb-3">
              <p className="text-sm text-gray-400">UPI ID:</p>
              <p className="font-mono text-lg text-yellow-400">{selectedOrder.users.upi_id}</p>
            </div>
          )}
          
          {selectedOrder.users.bank_details && (
            <div className="mb-3">
              <p className="text-sm text-gray-400">Bank Details:</p>
              <pre className="text-sm text-yellow-400 whitespace-pre-wrap">{selectedOrder.users.bank_details}</pre>
            </div>
          )}

          <div className="mt-4 p-3 bg-yellow-800/30 rounded">
            <p className="text-xs text-yellow-200">
              💡 Send exactly ₹{totalAmount.toFixed(2)} to the above payment details. 
              After payment, click "I Have Made Payment" and upload your payment proof.
            </p>
          </div>
        </div>

        <div className="flex gap-4">
          <button
            onClick={() => setStep('orders')}
            className="flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800"
          >
            ← Back
          </button>
          <button
            onClick={handlePaymentMade}
            className="flex-1 btn-primary"
          >
            I Have Made Payment
          </button>
        </div>
      </div>
    )
  }

  if (step === 'proof' && selectedOrder) {
    return (
      <div className="max-w-md mx-auto">
        <div className="mb-6 p-4 bg-purple-900/20 border border-purple-500/30 rounded-lg">
          <h2 className="text-2xl font-bold mb-2 text-center text-purple-400">Upload Payment Proof</h2>
          <p className="text-gray-300 text-center text-sm">
            Upload screenshot of your payment
          </p>
        </div>

        <div className="mb-6">
          <label htmlFor="proof" className="block text-sm font-medium mb-2">
            Payment Proof Image
          </label>
          <input
            type="file"
            id="proof"
            accept="image/*"
            onChange={handleFileChange}
            className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
            required
          />
          <p className="text-xs text-gray-500 mt-2">
            Supported formats: JPG, PNG, GIF (Max 5MB)
          </p>
        </div>

        {proofFile && (
          <div className="mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
            <p className="text-green-400 text-sm">
              ✅ File selected: {proofFile.name}
            </p>
          </div>
        )}

        {error && (
          <div className="mb-4 text-red-500 text-sm p-2 bg-red-900/20 rounded">
            {error}
          </div>
        )}

        <div className="flex gap-4">
          <button
            onClick={() => setStep('payment')}
            className="flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800"
          >
            ← Back
          </button>
          <button
            onClick={handleSubmitProof}
            disabled={!proofFile || uploading}
            className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {uploading ? 'Uploading...' : 'Submit Proof'}
          </button>
        </div>
      </div>
    )
  }

  if (step === 'success') {
    return (
      <div className="max-w-md mx-auto text-center">
        <div className="mb-6 p-6 bg-green-900/20 border border-green-500/30 rounded-lg">
          <h2 className="text-2xl font-bold mb-4 text-green-400">✅ Proof Submitted!</h2>
          <p className="text-gray-300 mb-2">Your payment proof has been submitted successfully.</p>
          <p className="text-sm text-gray-500">Admin will verify your payment and release USDT to your wallet.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
        <h2 className="text-2xl font-bold mb-2 text-center text-green-400">Buy USDT</h2>
        <p className="text-gray-300 text-center text-sm">
          Choose from available sell orders
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {sellOrders.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No sell orders available at the moment.</p>
            <button
              onClick={loadSellOrders}
              className="mt-4 btn-primary px-6 py-2"
            >
              Refresh
            </button>
          </div>
        ) : (
          sellOrders.map((order) => (
            <div key={order.id} className="p-4 bg-gray-800 border border-gray-700 rounded-lg hover:border-green-500/50 transition-colors">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                <div>
                  <p className="text-sm text-gray-400">Amount</p>
                  <p className="font-semibold text-lg">{order.amount} USDT</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Rate</p>
                  <p className="font-semibold">₹{order.rate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Total</p>
                  <p className="font-semibold text-green-400">₹{(order.amount * order.rate).toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Seller</p>
                  <p className="font-mono text-sm">{formatWallet(order.user_wallet)}</p>
                  <p className="text-xs text-gray-500">{formatDate(order.created_at)}</p>
                </div>
                <div>
                  <button
                    onClick={() => handleSelectOrder(order)}
                    className="btn-primary w-full"
                    disabled={order.user_wallet === address}
                  >
                    {order.user_wallet === address ? 'Your Order' : 'Buy'}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-6 text-center">
        <button
          onClick={onClose}
          className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800"
        >
          Close
        </button>
      </div>
    </div>
  )
}
