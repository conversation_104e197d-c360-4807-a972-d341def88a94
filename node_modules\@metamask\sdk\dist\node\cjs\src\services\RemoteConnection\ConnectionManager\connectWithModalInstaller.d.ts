import { RemoteConnectionProps, RemoteConnectionState } from '../RemoteConnection';
/**
 * Handles the disconnection process for a MetaMask connection based on the current state and provided options.
 *
 * @param state Current state of the RemoteConnection class instance.
 * @param options Configuration options for the disconnection.
 * @returns Promise<void>
 */
export declare function connectWithModalInstaller(state: RemoteConnectionState, options: RemoteConnectionProps, linkParams: string): Promise<void>;
//# sourceMappingURL=connectWithModalInstaller.d.ts.map